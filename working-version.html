<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Link Manager Pro - Working Version</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
    <div class="container mx-auto p-4 max-w-7xl">
        <!-- Header -->
        <header class="glass-card p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Link Manager Pro
                </h1>
                <div class="flex items-center space-x-3">
                    <button id="darkModeToggle" class="glass-button">
                        <i data-lucide="sun" class="w-4 h-4 dark:hidden"></i>
                        <i data-lucide="moon" class="w-4 h-4 hidden dark:block"></i>
                    </button>
                    <button id="settingsBtn" class="glass-button">
                        <i data-lucide="settings" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
            
            <!-- Search Bar -->
            <div class="flex gap-4 items-center">
                <div class="flex-1 relative">
                    <input id="searchInput" type="text" placeholder="Search links..." 
                           class="w-full glass-input pl-10 pr-4 py-3">
                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"></i>
                </div>
                <button id="addLinkBtn" class="glass-button-primary">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Add Link
                </button>
            </div>
        </header>

        <!-- Link Container -->
        <main>
            <div id="linkContainer" class="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"></div>
            <div id="emptyState" class="hidden text-center py-16">
                <div class="glass-card p-8 max-w-md mx-auto">
                    <div class="text-6xl mb-4">🔗</div>
                    <h3 class="text-xl font-semibold mb-2">No links yet</h3>
                    <p class="text-gray-500 mb-4">Start building your collection!</p>
                    <button onclick="openAddModal()" class="glass-button-primary">
                        Add Your First Link
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- Add Link Modal -->
    <div id="addModal" class="modal hidden">
        <div class="modal-backdrop" onclick="closeAddModal()"></div>
        <div class="modal-content glass-card">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold">Add New Link</h2>
                <button onclick="closeAddModal()">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            
            <form id="addLinkForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2">URL *</label>
                    <input id="url" type="url" placeholder="https://example.com" class="glass-input w-full" required>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Title *</label>
                    <input id="title" type="text" placeholder="Link title" class="glass-input w-full" required>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Description</label>
                    <textarea id="description" placeholder="Optional description" class="glass-input w-full h-20"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Tags</label>
                    <input id="tags" type="text" placeholder="work, social, tools" class="glass-input w-full">
                </div>
                
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeAddModal()" class="glass-button">Cancel</button>
                    <button type="submit" class="glass-button-primary">Add Link</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <script>
        // Simple Link Manager Implementation
        class SimpleLinkManager {
            constructor() {
                this.links = JSON.parse(localStorage.getItem('links')) || [];
                this.isDarkMode = localStorage.getItem('darkMode') === 'true';
                this.searchTerm = '';
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.applyDarkMode();
                this.renderLinks();
                lucide.createIcons();
            }

            setupEventListeners() {
                // Add link button
                document.getElementById('addLinkBtn').addEventListener('click', () => this.openAddModal());
                
                // Form submission
                document.getElementById('addLinkForm').addEventListener('submit', (e) => this.handleAddLink(e));
                
                // Search
                document.getElementById('searchInput').addEventListener('input', (e) => this.handleSearch(e));
                
                // Dark mode toggle
                document.getElementById('darkModeToggle').addEventListener('click', () => this.toggleDarkMode());
                
                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                        e.preventDefault();
                        this.openAddModal();
                    }
                    if (e.key === 'Escape') {
                        this.closeAddModal();
                    }
                });
            }

            handleAddLink(e) {
                e.preventDefault();
                
                const url = document.getElementById('url').value.trim();
                const title = document.getElementById('title').value.trim();
                const description = document.getElementById('description').value.trim();
                const tags = document.getElementById('tags').value.trim();
                
                if (!url || !title) {
                    this.showToast('URL and title are required', 'error');
                    return;
                }

                const newLink = {
                    id: Date.now().toString(),
                    url: url,
                    title: title,
                    description: description,
                    tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
                    dateAdded: new Date().toISOString()
                };

                this.links.push(newLink);
                this.saveLinks();
                this.renderLinks();
                this.closeAddModal();
                this.resetForm();
                this.showToast('Link added successfully!', 'success');
            }

            handleSearch(e) {
                this.searchTerm = e.target.value.toLowerCase();
                this.renderLinks();
            }

            renderLinks() {
                const container = document.getElementById('linkContainer');
                const emptyState = document.getElementById('emptyState');
                
                const filteredLinks = this.getFilteredLinks();
                
                if (filteredLinks.length === 0) {
                    container.innerHTML = '';
                    emptyState.classList.remove('hidden');
                    return;
                }
                
                emptyState.classList.add('hidden');
                container.innerHTML = '';
                
                filteredLinks.forEach(link => {
                    const card = this.createLinkCard(link);
                    container.appendChild(card);
                });
            }

            createLinkCard(link) {
                const card = document.createElement('div');
                card.className = 'glass-card p-6 hover:transform hover:scale-105 transition-all duration-300';
                
                card.innerHTML = `
                    <div class="flex justify-between items-start mb-3">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white line-clamp-2 flex-1">
                            ${link.title}
                        </h3>
                        <div class="flex space-x-2 ml-2">
                            <button onclick="linkManager.editLink('${link.id}')" class="text-blue-500 hover:text-blue-700">
                                <i data-lucide="edit-2" class="w-4 h-4"></i>
                            </button>
                            <button onclick="linkManager.deleteLink('${link.id}')" class="text-red-500 hover:text-red-700">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    
                    ${link.description ? `<p class="text-sm text-gray-600 dark:text-gray-400 mb-3">${link.description}</p>` : ''}
                    
                    ${link.tags && link.tags.length > 0 ? `
                        <div class="flex flex-wrap gap-1 mb-3">
                            ${link.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                    ` : ''}
                    
                    <a href="${link.url}" target="_blank" rel="noopener noreferrer" 
                       class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 font-medium">
                        Visit Link
                        <i data-lucide="external-link" class="w-4 h-4 ml-1"></i>
                    </a>
                `;
                
                return card;
            }

            getFilteredLinks() {
                if (!this.searchTerm) return this.links;
                
                return this.links.filter(link => 
                    link.title.toLowerCase().includes(this.searchTerm) ||
                    link.url.toLowerCase().includes(this.searchTerm) ||
                    link.description.toLowerCase().includes(this.searchTerm) ||
                    (link.tags && link.tags.some(tag => tag.toLowerCase().includes(this.searchTerm)))
                );
            }

            deleteLink(linkId) {
                if (confirm('Delete this link?')) {
                    this.links = this.links.filter(link => link.id !== linkId);
                    this.saveLinks();
                    this.renderLinks();
                    this.showToast('Link deleted!', 'success');
                }
            }

            editLink(linkId) {
                const link = this.links.find(l => l.id === linkId);
                if (!link) return;
                
                document.getElementById('url').value = link.url;
                document.getElementById('title').value = link.title;
                document.getElementById('description').value = link.description || '';
                document.getElementById('tags').value = link.tags ? link.tags.join(', ') : '';
                
                // Store editing state
                this.editingLinkId = linkId;
                this.openAddModal();
            }

            toggleDarkMode() {
                this.isDarkMode = !this.isDarkMode;
                this.applyDarkMode();
                localStorage.setItem('darkMode', this.isDarkMode);
            }

            applyDarkMode() {
                document.documentElement.classList.toggle('dark', this.isDarkMode);
            }

            openAddModal() {
                document.getElementById('addModal').classList.remove('hidden');
                document.getElementById('url').focus();
            }

            closeAddModal() {
                document.getElementById('addModal').classList.add('hidden');
                this.resetForm();
                this.editingLinkId = null;
            }

            resetForm() {
                document.getElementById('addLinkForm').reset();
            }

            saveLinks() {
                localStorage.setItem('links', JSON.stringify(this.links));
            }

            showToast(message, type = 'info') {
                const container = document.getElementById('toastContainer');
                const toast = document.createElement('div');
                toast.className = `toast ${type} show`;
                
                toast.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <i data-lucide="${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : 'info'}" class="w-5 h-5"></i>
                        <span>${message}</span>
                        <button onclick="this.parentElement.parentElement.remove()">
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                    </div>
                `;
                
                container.appendChild(toast);
                lucide.createIcons();
                
                setTimeout(() => toast.remove(), 3000);
            }
        }

        // Global functions
        function openAddModal() {
            linkManager.openAddModal();
        }

        function closeAddModal() {
            linkManager.closeAddModal();
        }

        // Initialize app
        const linkManager = new SimpleLinkManager();
    </script>
</body>
</html>
