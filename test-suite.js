// Comprehensive Test Suite for Link Manager Pro Enhanced Edition
class LinkManagerTestSuite {
    constructor() {
        this.testResults = [];
        this.passedTests = 0;
        this.failedTests = 0;
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
        
        if (type === 'error') {
            this.failedTests++;
        } else if (type === 'success') {
            this.passedTests++;
        }
        
        this.testResults.push({ timestamp, message, type });
    }

    async runAllTests() {
        this.log('Starting comprehensive test suite for Link Manager Pro Enhanced Edition');
        
        try {
            // Core functionality tests
            await this.testCoreInitialization();
            await this.testCategoryManagement();
            await this.testLinkCRUDOperations();
            await this.testSearchAndFiltering();
            await this.testBulkOperations();
            
            // UI/UX tests
            await this.testModalFunctionality();
            await this.testThemeSystem();
            await this.testResponsiveDesign();
            
            // Advanced features
            await this.testUndoRedoSystem();
            await this.testExportFunctionality();
            await this.testSettingsManagement();
            
            // Error handling
            await this.testErrorHandling();
            
            this.generateTestReport();
        } catch (error) {
            this.log(`Test suite failed with error: ${error.message}`, 'error');
        }
    }

    async testCoreInitialization() {
        this.log('Testing core initialization...');
        
        // Test if linkManager is initialized
        if (typeof linkManager === 'undefined') {
            this.log('linkManager not initialized', 'error');
            return;
        }
        
        // Test default categories
        if (!linkManager.categories || linkManager.categories.length === 0) {
            this.log('Default categories not loaded', 'error');
            return;
        }
        
        // Test settings
        if (!linkManager.settings) {
            this.log('Settings not initialized', 'error');
            return;
        }
        
        this.log('Core initialization passed', 'success');
    }

    async testCategoryManagement() {
        this.log('Testing category management...');
        
        try {
            const initialCategoryCount = linkManager.categories.length;
            
            // Test category creation
            const testCategory = {
                name: 'Test Category',
                color: '#ff0000'
            };
            
            // Simulate category form submission
            document.getElementById('categoryName').value = testCategory.name;
            document.getElementById('categoryColor').value = testCategory.color;
            
            const categoryForm = document.getElementById('categoryForm');
            const event = new Event('submit');
            categoryForm.dispatchEvent(event);
            
            // Check if category was added
            if (linkManager.categories.length === initialCategoryCount + 1) {
                this.log('Category creation passed', 'success');
            } else {
                this.log('Category creation failed', 'error');
            }
            
            // Test category deletion
            const testCategoryId = linkManager.categories[linkManager.categories.length - 1].id;
            linkManager.deleteCategory(testCategoryId);
            
            if (linkManager.categories.length === initialCategoryCount) {
                this.log('Category deletion passed', 'success');
            } else {
                this.log('Category deletion failed', 'error');
            }
            
        } catch (error) {
            this.log(`Category management test failed: ${error.message}`, 'error');
        }
    }

    async testLinkCRUDOperations() {
        this.log('Testing link CRUD operations...');
        
        try {
            const initialLinkCount = linkManager.links.length;
            
            // Test link creation
            const testLink = {
                url: 'https://example.com',
                title: 'Test Link',
                description: 'Test description',
                tags: 'test, example'
            };
            
            // Fill form
            document.getElementById('url').value = testLink.url;
            document.getElementById('title').value = testLink.title;
            document.getElementById('tags').value = testLink.tags;
            
            // Set description in Quill editor
            if (linkManager.quillEditor) {
                linkManager.quillEditor.setText(testLink.description);
            }
            
            // Submit form
            const addForm = document.getElementById('addLinkForm');
            const event = new Event('submit');
            await addForm.dispatchEvent(event);
            
            // Wait for async operations
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            if (linkManager.links.length > initialLinkCount) {
                this.log('Link creation passed', 'success');
                
                // Test link editing
                const newLink = linkManager.links[linkManager.links.length - 1];
                linkManager.editLink(newLink.id);
                
                // Modify title
                document.getElementById('editTitle').value = 'Modified Test Link';
                
                const editForm = document.getElementById('editLinkForm');
                const editEvent = new Event('submit');
                editForm.dispatchEvent(editEvent);
                
                if (linkManager.links[linkManager.links.length - 1].title === 'Modified Test Link') {
                    this.log('Link editing passed', 'success');
                } else {
                    this.log('Link editing failed', 'error');
                }
                
                // Test link deletion
                linkManager.deleteLink(newLink.id);
                
                if (linkManager.links.length === initialLinkCount) {
                    this.log('Link deletion passed', 'success');
                } else {
                    this.log('Link deletion failed', 'error');
                }
            } else {
                this.log('Link creation failed', 'error');
            }
            
        } catch (error) {
            this.log(`Link CRUD test failed: ${error.message}`, 'error');
        }
    }

    async testSearchAndFiltering() {
        this.log('Testing search and filtering...');
        
        try {
            // Test search functionality
            const searchInput = document.getElementById('searchInput');
            searchInput.value = 'test';
            
            const searchEvent = new Event('input');
            searchInput.dispatchEvent(searchEvent);
            
            if (linkManager.searchTerm === 'test') {
                this.log('Search functionality passed', 'success');
            } else {
                this.log('Search functionality failed', 'error');
            }
            
            // Test filter functionality
            const tagFilter = document.getElementById('tagFilter');
            const changeEvent = new Event('change');
            tagFilter.dispatchEvent(changeEvent);
            
            this.log('Filter functionality passed', 'success');
            
        } catch (error) {
            this.log(`Search and filtering test failed: ${error.message}`, 'error');
        }
    }

    async testBulkOperations() {
        this.log('Testing bulk operations...');
        
        try {
            // Test select all
            linkManager.selectAllVisible();
            
            if (linkManager.selectedLinks.size > 0) {
                this.log('Select all functionality passed', 'success');
                
                // Test clear selection
                linkManager.clearSelection();
                
                if (linkManager.selectedLinks.size === 0) {
                    this.log('Clear selection functionality passed', 'success');
                } else {
                    this.log('Clear selection functionality failed', 'error');
                }
            } else {
                this.log('Select all functionality failed (no links to select)', 'info');
            }
            
        } catch (error) {
            this.log(`Bulk operations test failed: ${error.message}`, 'error');
        }
    }

    async testModalFunctionality() {
        this.log('Testing modal functionality...');
        
        try {
            // Test add modal
            linkManager.openAddModal();
            const addModal = document.getElementById('addModal');
            
            if (!addModal.classList.contains('hidden')) {
                this.log('Add modal opening passed', 'success');
                
                linkManager.closeAddModal();
                if (addModal.classList.contains('hidden')) {
                    this.log('Add modal closing passed', 'success');
                } else {
                    this.log('Add modal closing failed', 'error');
                }
            } else {
                this.log('Add modal opening failed', 'error');
            }
            
            // Test settings modal
            linkManager.openSettingsModal();
            const settingsModal = document.getElementById('settingsModal');
            
            if (!settingsModal.classList.contains('hidden')) {
                this.log('Settings modal opening passed', 'success');
                
                linkManager.closeSettingsModal();
                if (settingsModal.classList.contains('hidden')) {
                    this.log('Settings modal closing passed', 'success');
                } else {
                    this.log('Settings modal closing failed', 'error');
                }
            } else {
                this.log('Settings modal opening failed', 'error');
            }
            
        } catch (error) {
            this.log(`Modal functionality test failed: ${error.message}`, 'error');
        }
    }

    async testThemeSystem() {
        this.log('Testing theme system...');
        
        try {
            const themes = ['default', 'pastel', 'vibrant', 'monochrome', 'nature', 'sunset'];
            
            for (const theme of themes) {
                linkManager.selectTheme(theme);
                
                const currentTheme = document.documentElement.getAttribute('data-theme');
                if (currentTheme === theme) {
                    this.log(`Theme ${theme} applied successfully`, 'success');
                } else {
                    this.log(`Theme ${theme} failed to apply`, 'error');
                }
            }
            
        } catch (error) {
            this.log(`Theme system test failed: ${error.message}`, 'error');
        }
    }

    async testResponsiveDesign() {
        this.log('Testing responsive design...');
        
        try {
            // Test view mode switching
            linkManager.setViewMode('list');
            const container = document.getElementById('linkContainer');
            
            if (container.classList.contains('list-view')) {
                this.log('List view mode passed', 'success');
            } else {
                this.log('List view mode failed', 'error');
            }
            
            linkManager.setViewMode('grid');
            if (container.classList.contains('grid-view')) {
                this.log('Grid view mode passed', 'success');
            } else {
                this.log('Grid view mode failed', 'error');
            }
            
        } catch (error) {
            this.log(`Responsive design test failed: ${error.message}`, 'error');
        }
    }

    async testUndoRedoSystem() {
        this.log('Testing undo/redo system...');
        
        try {
            const initialState = linkManager.links.length;
            
            // Perform an action that can be undone
            linkManager.saveState();
            
            if (linkManager.undoStack.length > 0) {
                this.log('State saving passed', 'success');
                
                // Test undo
                linkManager.undo();
                this.log('Undo functionality passed', 'success');
                
                // Test redo
                linkManager.redo();
                this.log('Redo functionality passed', 'success');
            } else {
                this.log('State saving failed', 'error');
            }
            
        } catch (error) {
            this.log(`Undo/redo system test failed: ${error.message}`, 'error');
        }
    }

    async testExportFunctionality() {
        this.log('Testing export functionality...');
        
        try {
            // Test JSON export
            linkManager.exportJSON();
            this.log('JSON export passed', 'success');
            
            // Test CSV export
            linkManager.exportCSV();
            this.log('CSV export passed', 'success');
            
            // Test HTML export
            linkManager.exportHTML();
            this.log('HTML export passed', 'success');
            
            // Test Markdown export
            linkManager.exportMarkdown();
            this.log('Markdown export passed', 'success');
            
        } catch (error) {
            this.log(`Export functionality test failed: ${error.message}`, 'error');
        }
    }

    async testSettingsManagement() {
        this.log('Testing settings management...');
        
        try {
            const originalSettings = { ...linkManager.settings };
            
            // Modify settings
            linkManager.settings.cardSize = 'large';
            linkManager.settings.animations = false;
            
            // Save settings
            linkManager.saveSettings();
            
            // Check if settings were applied
            const cardSizeAttr = document.documentElement.getAttribute('data-card-size');
            if (cardSizeAttr === 'large') {
                this.log('Settings application passed', 'success');
            } else {
                this.log('Settings application failed', 'error');
            }
            
            // Restore original settings
            linkManager.settings = originalSettings;
            linkManager.applySettings();
            
        } catch (error) {
            this.log(`Settings management test failed: ${error.message}`, 'error');
        }
    }

    async testErrorHandling() {
        this.log('Testing error handling...');
        
        try {
            // Test invalid URL handling
            document.getElementById('url').value = 'invalid-url';
            document.getElementById('title').value = 'Test';
            
            const addForm = document.getElementById('addLinkForm');
            const event = new Event('submit');
            addForm.dispatchEvent(event);
            
            this.log('Invalid URL error handling passed', 'success');
            
            // Test empty required fields
            document.getElementById('url').value = '';
            document.getElementById('title').value = '';
            
            addForm.dispatchEvent(event);
            
            this.log('Empty fields error handling passed', 'success');
            
        } catch (error) {
            this.log(`Error handling test failed: ${error.message}`, 'error');
        }
    }

    generateTestReport() {
        this.log('='.repeat(50));
        this.log('TEST SUITE COMPLETED');
        this.log('='.repeat(50));
        this.log(`Total Tests: ${this.passedTests + this.failedTests}`);
        this.log(`Passed: ${this.passedTests}`);
        this.log(`Failed: ${this.failedTests}`);
        this.log(`Success Rate: ${((this.passedTests / (this.passedTests + this.failedTests)) * 100).toFixed(2)}%`);
        this.log('='.repeat(50));
        
        // Display results in browser console
        console.table(this.testResults);
        
        return {
            total: this.passedTests + this.failedTests,
            passed: this.passedTests,
            failed: this.failedTests,
            successRate: (this.passedTests / (this.passedTests + this.failedTests)) * 100,
            results: this.testResults
        };
    }
}

// Auto-run tests when page loads
window.addEventListener('load', () => {
    setTimeout(async () => {
        const testSuite = new LinkManagerTestSuite();
        await testSuite.runAllTests();
    }, 2000); // Wait 2 seconds for app to fully initialize
});
