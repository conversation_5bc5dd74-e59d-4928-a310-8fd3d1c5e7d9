# 🔗 Link Manager Pro

A beautiful, modern link management application with glassmorphism UI design and powerful features for organizing your favorite links.

![Link Manager Pro](https://img.shields.io/badge/Version-2.0-blue) ![HTML5](https://img.shields.io/badge/HTML5-E34F26?logo=html5&logoColor=white) ![CSS3](https://img.shields.io/badge/CSS3-1572B6?logo=css3&logoColor=white) ![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?logo=javascript&logoColor=black)

## ✨ Features

### 🌐 Smart Link Previews
- **Automatic metadata fetching** using Microlink API
- **Rich previews** with thumbnails, titles, and descriptions
- **Fallback handling** with favicon and domain extraction
- **Intelligent caching** for improved performance
- **Domain-specific handling** for YouTube, Twitter, GitHub, etc.

### 💎 Glassmorphism UI Design
- **Semi-transparent cards** with backdrop blur effects
- **Smooth animations** and hover transitions
- **Dark/Light mode toggle** with system preference detection
- **Modern typography** and spacing
- **Responsive design** for all screen sizes
- **Floating Action Button** for quick link addition

### ⚙️ Advanced Functionality
- **Real-time search** across titles, descriptions, URLs, and tags
- **Multi-filter system** by tags and domains
- **Drag-and-drop reordering** with visual feedback
- **Tag management** with comma-separated input
- **Grid/List view toggle** for different browsing preferences
- **Animated card entry/exit** effects
- **Smart form auto-filling** from URL previews

### 💾 Data Management
- **Local storage** for offline functionality
- **JSON export/import** for backup and sharing
- **Preview caching** with 24-hour expiration
- **Automatic data persistence** across sessions

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Local web server (for CORS compliance)

### Installation

1. **Clone or download** the project files
2. **Start a local server**:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```
3. **Open your browser** and navigate to `http://localhost:8000/main.html`

## 📖 Usage Guide

### Adding Links
1. Click the **floating action button** (+ icon) or the "Add Your First Link" button
2. **Paste a URL** - the app will automatically fetch preview information
3. **Customize** the title, description, and tags as needed
4. Click **"Add Link"** to save

### Managing Links
- **Edit**: Click the edit icon on any link card
- **Delete**: Click the delete icon (with confirmation)
- **Reorder**: Drag and drop cards to rearrange them
- **Search**: Use the search bar to find specific links
- **Filter**: Use tag and domain filters to narrow down results

### View Options
- **Grid View**: Card-based layout (default)
- **List View**: Compact horizontal layout
- **Dark Mode**: Toggle between light and dark themes

### Data Management
- **Export**: Download your links as a JSON file
- **Import**: Upload a previously exported JSON file
- **Automatic Backup**: All data is saved locally in your browser

## 🛠️ Technical Details

### Architecture
- **Frontend**: Pure HTML5, CSS3, and JavaScript (ES6+)
- **Styling**: Tailwind CSS + Custom Glassmorphism CSS
- **Preview API**: Microlink.io for metadata fetching
- **Drag & Drop**: SortableJS library
- **Storage**: Browser localStorage with JSON serialization

### File Structure
```
├── main.html           # Main application HTML
├── styles.css          # Glassmorphism styles and animations
├── script.js           # Core application logic
├── preview-service.js  # Link preview fetching service
└── README.md          # This documentation
```

### Browser Compatibility
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## 🎨 Customization

### Themes
The app supports both light and dark modes with automatic system preference detection. You can customize colors by modifying the CSS variables in `styles.css`:

```css
:root {
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
```

### Preview Service
The link preview service uses Microlink API by default. You can customize or replace it by modifying `preview-service.js`:

- **Free tier**: 100 requests/day
- **Alternative APIs**: LinkPreview.net, OpenGraph.io
- **Self-hosted**: Implement your own metadata extraction

## 🔧 Advanced Configuration

### API Configuration
To use a different preview service, modify the `LinkPreviewService` class in `preview-service.js`:

```javascript
constructor() {
    this.baseUrl = 'https://api.microlink.io'; // Change this
    this.apiKey = 'your-api-key'; // Add if required
}
```

### Storage Options
The app uses localStorage by default. To implement cloud storage:

1. Modify the `saveLinks()` method in `script.js`
2. Add authentication and API calls
3. Implement sync functionality

## 🐛 Troubleshooting

### Common Issues

**Links not loading previews:**
- Check internet connection
- Verify the URL is accessible
- API rate limits may apply (100/day for free tier)

**Drag and drop not working:**
- Ensure SortableJS is loaded
- Check browser console for errors
- Try refreshing the page

**Dark mode not persisting:**
- Check if localStorage is enabled
- Clear browser cache if needed

**Import/Export not working:**
- Ensure file is valid JSON format
- Check browser's download settings
- Verify file permissions

## 🤝 Contributing

This is a client-side application that can be easily extended:

1. **Add new preview sources** in `preview-service.js`
2. **Enhance UI components** in `styles.css`
3. **Extend functionality** in `script.js`
4. **Add new themes** or color schemes

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **Microlink.io** for the preview API
- **Tailwind CSS** for the utility framework
- **SortableJS** for drag-and-drop functionality
- **Glassmorphism** design inspiration from modern UI trends

---

**Enjoy managing your links with style! 🎉**
