<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Link Manager</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 min-h-screen p-4">
    <div class="container mx-auto">
        <h1 class="text-3xl font-bold mb-4 text-center">Link Manager</h1>

        <!-- Add Link Form -->
        <div class="bg-white p-4 rounded shadow mb-6">
            <h2 class="text-xl font-semibold mb-2">Add New Link</h2>
            <div class="flex flex-col gap-2">
                <input id="title" type="text" placeholder="Title" class="p-2 border rounded" />
                <input id="url" type="url" placeholder="URL" class="p-2 border rounded" />
                <input id="desc" type="text" placeholder="Description (optional)" class="p-2 border rounded" />
                <button onclick="addLink()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Add
                    Link</button>
            </div>
        </div>

        <!-- Link Cards -->
        <div id="linkContainer" class="grid md:grid-cols-3 gap-4"></div>
    </div>

    <script>
        let links = JSON.parse(localStorage.getItem('links')) || [];

        function saveLinks() {
            localStorage.setItem('links', JSON.stringify(links));
        }

        function addLink() {
            const title = document.getElementById('title').value.trim();
            const url = document.getElementById('url').value.trim();
            const desc = document.getElementById('desc').value.trim();
            if (!title || !url) return alert('Title and URL are required');
            links.push({ title, url, desc });
            saveLinks();
            renderLinks();
            document.getElementById('title').value = '';
            document.getElementById('url').value = '';
            document.getElementById('desc').value = '';
        }

        function deleteLink(index) {
            links.splice(index, 1);
            saveLinks();
            renderLinks();
        }

        function editLink(index) {
            const card = document.getElementById(`card-${index}`);
            const link = links[index];
            card.innerHTML = `
        <input id="edit-title-${index}" value="${link.title}" class="p-2 border rounded w-full mb-1" />
        <input id="edit-url-${index}" value="${link.url}" class="p-2 border rounded w-full mb-1" />
        <input id="edit-desc-${index}" value="${link.desc || ''}" class="p-2 border rounded w-full mb-2" />
        <div class="flex justify-between">
          <button onclick="saveEdit(${index})" class="bg-green-500 text-white px-2 py-1 rounded hover:bg-green-600">Save</button>
          <button onclick="renderLinks()" class="bg-gray-400 text-white px-2 py-1 rounded hover:bg-gray-500">Cancel</button>
        </div>`;
        }

        function saveEdit(index) {
            const title = document.getElementById(`edit-title-${index}`).value;
            const url = document.getElementById(`edit-url-${index}`).value;
            const desc = document.getElementById(`edit-desc-${index}`).value;
            links[index] = { title, url, desc };
            saveLinks();
            renderLinks();
        }

        function renderLinks() {
            const container = document.getElementById('linkContainer');
            container.innerHTML = '';
            links.forEach((link, index) => {
                const card = document.createElement('div');
                card.className = 'bg-white p-4 rounded shadow';
                card.id = `card-${index}`;
                card.innerHTML = `
          <h3 class="text-lg font-bold">${link.title}</h3>
          <p class="text-sm text-gray-600 mb-2">${link.desc || ''}</p>
          <a href="${link.url}" target="_blank" class="text-blue-500 underline">Visit</a>
          <div class="mt-4 flex justify-between">
            <button onclick="editLink(${index})" class="text-yellow-600 hover:underline">Edit</button>
            <button onclick="deleteLink(${index})" class="text-red-600 hover:underline">Delete</button>
          </div>
        `;
                container.appendChild(card);
            });
        }

        renderLinks();
    </script>
</body>

</html>