<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Link Manager Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    backdropBlur: {
                        xs: '2px',
                    }
                }
            }
        }
    </script>
</head>

<body class="min-h-screen transition-all duration-300 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
    <!-- Background Pattern -->
    <div class="fixed inset-0 opacity-30 dark:opacity-20">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
    </div>

    <div class="relative z-10 container mx-auto p-4 max-w-7xl">
        <!-- Header -->
        <header class="glass-card p-6 mb-8 text-center">
            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center space-x-4">
                    <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        Link Manager Pro
                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- View Toggle -->
                    <div class="glass-button-group">
                        <button id="gridViewBtn" class="view-btn active" title="Grid View">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                            </svg>
                        </button>
                        <button id="listViewBtn" class="view-btn" title="List View">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                            </svg>
                        </button>
                    </div>
                    <!-- Dark Mode Toggle -->
                    <button id="darkModeToggle" class="glass-button" title="Toggle Dark Mode">
                        <svg class="w-5 h-5 dark:hidden" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"/>
                        </svg>
                        <svg class="w-5 h-5 hidden dark:block" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"/>
                        </svg>
                    </button>
                    <!-- Export/Import -->
                    <div class="relative">
                        <button id="menuToggle" class="glass-button" title="Menu">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                            </svg>
                        </button>
                        <div id="dropdownMenu" class="hidden absolute right-0 mt-2 w-48 glass-card rounded-lg shadow-lg z-50">
                            <button id="exportBtn" class="w-full text-left px-4 py-2 hover:bg-white/20 rounded-t-lg transition-colors">
                                📤 Export Links
                            </button>
                            <button id="importBtn" class="w-full text-left px-4 py-2 hover:bg-white/20 rounded-b-lg transition-colors">
                                📥 Import Links
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter Bar -->
            <div class="flex flex-col md:flex-row gap-4 items-center">
                <div class="flex-1 relative">
                    <input id="searchInput" type="text" placeholder="Search links..."
                           class="w-full glass-input pl-10 pr-4 py-3">
                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <select id="tagFilter" class="glass-input px-4 py-3 min-w-[150px]">
                    <option value="">All Tags</option>
                </select>
                <select id="domainFilter" class="glass-input px-4 py-3 min-w-[150px]">
                    <option value="">All Domains</option>
                </select>
            </div>
        </header>

        <!-- Link Container -->
        <main>
            <div id="linkContainer" class="grid-view grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"></div>
            <div id="emptyState" class="hidden text-center py-16">
                <div class="glass-card p-8 max-w-md mx-auto">
                    <div class="text-6xl mb-4">🔗</div>
                    <h3 class="text-xl font-semibold mb-2 text-gray-700 dark:text-gray-300">No links yet</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">Start building your link collection by adding your first link!</p>
                    <button onclick="openAddModal()" class="glass-button-primary">
                        Add Your First Link
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- Floating Action Button -->
    <button id="fab" class="fab" onclick="openAddModal()" title="Add New Link">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
        </svg>
    </button>

    <!-- Add Link Modal -->
    <div id="addModal" class="modal hidden">
        <div class="modal-backdrop" onclick="closeAddModal()"></div>
        <div class="modal-content glass-card">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Add New Link</h2>
                <button onclick="closeAddModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>

            <form id="addLinkForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">URL *</label>
                    <input id="url" type="url" placeholder="https://example.com" class="glass-input w-full" required>
                    <div id="urlPreview" class="hidden mt-3 p-3 glass-card border border-blue-200 dark:border-blue-800">
                        <div class="flex items-start space-x-3">
                            <img id="previewImage" class="w-16 h-16 rounded-lg object-cover flex-shrink-0" alt="Preview">
                            <div class="flex-1 min-w-0">
                                <h4 id="previewTitle" class="font-semibold text-gray-800 dark:text-white truncate"></h4>
                                <p id="previewDescription" class="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2"></p>
                                <p id="previewDomain" class="text-xs text-gray-500 dark:text-gray-500 mt-1"></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Title</label>
                    <input id="title" type="text" placeholder="Link title (auto-filled from URL)" class="glass-input w-full">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                    <textarea id="desc" placeholder="Optional description" class="glass-input w-full h-20 resize-none"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</label>
                    <input id="tags" type="text" placeholder="work, social, tools (comma-separated)" class="glass-input w-full">
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Separate tags with commas</p>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeAddModal()" class="glass-button">
                        Cancel
                    </button>
                    <button type="submit" class="glass-button-primary" id="addLinkBtn">
                        <span id="addBtnText">Add Link</span>
                        <span id="addBtnLoader" class="hidden">
                            <svg class="animate-spin w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                            </svg>
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="modal hidden">
        <div class="modal-backdrop" onclick="closeEditModal()"></div>
        <div class="modal-content glass-card">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Edit Link</h2>
                <button onclick="closeEditModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>

            <form id="editLinkForm" class="space-y-4">
                <input type="hidden" id="editIndex">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">URL *</label>
                    <input id="editUrl" type="url" class="glass-input w-full" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Title *</label>
                    <input id="editTitle" type="text" class="glass-input w-full" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                    <textarea id="editDesc" class="glass-input w-full h-20 resize-none"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</label>
                    <input id="editTags" type="text" class="glass-input w-full">
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeEditModal()" class="glass-button">
                        Cancel
                    </button>
                    <button type="submit" class="glass-button-primary">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Hidden file input for import -->
    <input type="file" id="fileInput" accept=".json" class="hidden">

    <script src="preview-service.js"></script>
    <script src="script.js"></script>
</body>

</html>