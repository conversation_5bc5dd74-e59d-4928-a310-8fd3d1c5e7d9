<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Link Manager Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.snow.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    backdropBlur: {
                        xs: '2px',
                    }
                }
            }
        }
    </script>
</head>

<body class="min-h-screen transition-all duration-300 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
    <!-- Background Pattern -->
    <div class="fixed inset-0 opacity-30 dark:opacity-20">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
    </div>

    <div class="relative z-10 container mx-auto p-4 max-w-7xl">
        <!-- Header -->
        <header class="glass-card p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <div class="flex items-center space-x-4">
                    <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        Link Manager Pro
                    </h1>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500 dark:text-gray-400" id="linkCount">0 links</span>
                        <span class="text-sm text-gray-400">•</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400" id="categoryCount">0 categories</span>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Bulk Actions -->
                    <div id="bulkActions" class="hidden flex items-center space-x-2 mr-4">
                        <span class="text-sm text-gray-600 dark:text-gray-400" id="selectedCount">0 selected</span>
                        <button id="bulkDeleteBtn" class="glass-button text-red-600 hover:text-red-700" title="Delete Selected">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                        <button id="bulkMoveBtn" class="glass-button" title="Move Selected">
                            <i data-lucide="folder-plus" class="w-4 h-4"></i>
                        </button>
                        <button id="bulkExportBtn" class="glass-button" title="Export Selected">
                            <i data-lucide="download" class="w-4 h-4"></i>
                        </button>
                        <button id="clearSelectionBtn" class="glass-button" title="Clear Selection">
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                    </div>

                    <!-- View Toggle -->
                    <div class="glass-button-group">
                        <button id="gridViewBtn" class="view-btn active" title="Grid View">
                            <i data-lucide="grid-3x3" class="w-4 h-4"></i>
                        </button>
                        <button id="listViewBtn" class="view-btn" title="List View">
                            <i data-lucide="list" class="w-4 h-4"></i>
                        </button>
                    </div>

                    <!-- Settings -->
                    <button id="settingsBtn" class="glass-button" title="Settings">
                        <i data-lucide="settings" class="w-4 h-4"></i>
                    </button>

                    <!-- Dark Mode Toggle -->
                    <button id="darkModeToggle" class="glass-button" title="Toggle Dark Mode">
                        <i data-lucide="sun" class="w-4 h-4 dark:hidden"></i>
                        <i data-lucide="moon" class="w-4 h-4 hidden dark:block"></i>
                    </button>

                    <!-- Menu -->
                    <div class="relative">
                        <button id="menuToggle" class="glass-button" title="Menu">
                            <i data-lucide="more-vertical" class="w-4 h-4"></i>
                        </button>
                        <div id="dropdownMenu" class="hidden absolute right-0 mt-2 w-56 glass-card rounded-lg shadow-lg z-50">
                            <div class="p-2">
                                <button id="exportMenuBtn" class="w-full text-left px-3 py-2 hover:bg-white/20 rounded-lg transition-colors flex items-center space-x-2">
                                    <i data-lucide="download" class="w-4 h-4"></i>
                                    <span>Export Options</span>
                                    <i data-lucide="chevron-right" class="w-4 h-4 ml-auto"></i>
                                </button>
                                <button id="importMenuBtn" class="w-full text-left px-3 py-2 hover:bg-white/20 rounded-lg transition-colors flex items-center space-x-2">
                                    <i data-lucide="upload" class="w-4 h-4"></i>
                                    <span>Import Options</span>
                                    <i data-lucide="chevron-right" class="w-4 h-4 ml-auto"></i>
                                </button>
                                <button id="shareBtn" class="w-full text-left px-3 py-2 hover:bg-white/20 rounded-lg transition-colors flex items-center space-x-2">
                                    <i data-lucide="share-2" class="w-4 h-4"></i>
                                    <span>Share Collection</span>
                                </button>
                                <hr class="my-2 border-white/20">
                                <button id="statsBtn" class="w-full text-left px-3 py-2 hover:bg-white/20 rounded-lg transition-colors flex items-center space-x-2">
                                    <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
                                    <span>Usage Statistics</span>
                                </button>
                                <button id="backupBtn" class="w-full text-left px-3 py-2 hover:bg-white/20 rounded-lg transition-colors flex items-center space-x-2">
                                    <i data-lucide="shield-check" class="w-4 h-4"></i>
                                    <span>Backup & Sync</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories Navigation -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-4 overflow-x-auto">
                    <button id="allCategoriesBtn" class="category-tab active" data-category="">
                        <i data-lucide="layers" class="w-4 h-4"></i>
                        <span>All Links</span>
                        <span class="category-count">0</span>
                    </button>
                    <div id="categoryTabs" class="flex items-center space-x-2">
                        <!-- Dynamic category tabs will be inserted here -->
                    </div>
                    <button id="addCategoryBtn" class="glass-button text-blue-600 hover:text-blue-700" title="Add Category">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                        <span class="hidden sm:inline ml-1">Add Category</span>
                    </button>
                </div>
                <div class="flex items-center space-x-2">
                    <button id="selectAllBtn" class="glass-button text-sm" title="Select All Visible">
                        <i data-lucide="check-square" class="w-4 h-4"></i>
                        <span class="hidden sm:inline ml-1">Select All</span>
                    </button>
                </div>
            </div>

            <!-- Search and Filter Bar -->
            <div class="flex flex-col lg:flex-row gap-4 items-center mb-6">
                <div class="flex-1 relative">
                    <input id="searchInput" type="text" placeholder="Search links, descriptions, tags..."
                           class="w-full glass-input pl-10 pr-10 py-3">
                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"></i>
                    <button id="clearSearchBtn" class="hidden absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
                <div class="flex flex-wrap gap-2">
                    <select id="tagFilter" class="glass-input px-4 py-3 min-w-[120px]">
                        <option value="">All Tags</option>
                    </select>
                    <select id="domainFilter" class="glass-input px-4 py-3 min-w-[120px]">
                        <option value="">All Domains</option>
                    </select>
                    <select id="sortFilter" class="glass-input px-4 py-3 min-w-[120px]">
                        <option value="dateAdded">Date Added</option>
                        <option value="title">Title A-Z</option>
                        <option value="domain">Domain</option>
                        <option value="lastVisited">Last Visited</option>
                    </select>
                    <button id="filterToggleBtn" class="glass-button" title="Advanced Filters">
                        <i data-lucide="filter" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>

            <!-- Advanced Filters Panel -->
            <div id="advancedFilters" class="hidden glass-card p-4 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date Range</label>
                        <div class="flex space-x-2">
                            <input type="date" id="dateFrom" class="glass-input flex-1">
                            <input type="date" id="dateTo" class="glass-input flex-1">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Link Type</label>
                        <select id="typeFilter" class="glass-input w-full">
                            <option value="">All Types</option>
                            <option value="youtube">YouTube</option>
                            <option value="github">GitHub</option>
                            <option value="twitter">Twitter</option>
                            <option value="article">Articles</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Has Preview</label>
                        <select id="previewFilter" class="glass-input w-full">
                            <option value="">All Links</option>
                            <option value="true">With Preview</option>
                            <option value="false">Without Preview</option>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end mt-4 space-x-2">
                    <button id="clearFiltersBtn" class="glass-button">Clear Filters</button>
                    <button id="applyFiltersBtn" class="glass-button-primary">Apply Filters</button>
                </div>
            </div>
        </header>

        <!-- Link Container -->
        <main>
            <!-- Category Section Template -->
            <div id="categoryContainer">
                <!-- Categories will be dynamically inserted here -->
            </div>

            <!-- All Links View -->
            <div id="allLinksContainer">
                <div id="linkContainer" class="grid-view grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"></div>
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="hidden text-center py-16">
                <div class="glass-card p-8 max-w-md mx-auto">
                    <div class="text-6xl mb-4">🔗</div>
                    <h3 class="text-xl font-semibold mb-2 text-gray-700 dark:text-gray-300">No links yet</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">Start building your link collection by adding your first link!</p>
                    <button onclick="openAddModal()" class="glass-button-primary">
                        Add Your First Link
                    </button>
                </div>
            </div>

            <!-- Category Empty State -->
            <div id="categoryEmptyState" class="hidden text-center py-16">
                <div class="glass-card p-8 max-w-md mx-auto">
                    <div class="text-6xl mb-4">📁</div>
                    <h3 class="text-xl font-semibold mb-2 text-gray-700 dark:text-gray-300">No links in this category</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">Add some links to this category or switch to another view.</p>
                    <div class="flex space-x-2 justify-center">
                        <button onclick="openAddModal()" class="glass-button-primary">
                            Add Link
                        </button>
                        <button onclick="linkManager.showAllCategories()" class="glass-button">
                            View All Links
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Floating Action Button -->
    <button id="fab" class="fab" onclick="openAddModal()" title="Add New Link">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
        </svg>
    </button>

    <!-- Add Link Modal -->
    <div id="addModal" class="modal hidden">
        <div class="modal-backdrop" onclick="closeAddModal()"></div>
        <div class="modal-content glass-card max-w-2xl">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Add New Link</h2>
                <button onclick="closeAddModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <form id="addLinkForm" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">URL *</label>
                    <input id="url" type="url" placeholder="https://example.com" class="glass-input w-full" required>
                    <div id="urlPreview" class="hidden mt-3 p-4 glass-card border border-blue-200 dark:border-blue-800">
                        <div class="flex items-start space-x-4">
                            <img id="previewImage" class="w-20 h-20 rounded-lg object-cover flex-shrink-0" alt="Preview">
                            <div class="flex-1 min-w-0">
                                <h4 id="previewTitle" class="font-semibold text-gray-800 dark:text-white truncate"></h4>
                                <p id="previewDescription" class="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-3"></p>
                                <p id="previewDomain" class="text-xs text-gray-500 dark:text-gray-500 mt-2"></p>
                            </div>
                            <button type="button" id="customImageBtn" class="glass-button text-sm" title="Custom Image">
                                <i data-lucide="image" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Title *</label>
                        <input id="title" type="text" placeholder="Link title (auto-filled from URL)" class="glass-input w-full" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Categories</label>
                        <div class="relative">
                            <select id="categories" multiple class="glass-input w-full" style="height: 42px;">
                                <!-- Categories will be populated dynamically -->
                            </select>
                            <button type="button" id="newCategoryBtn" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-600 hover:text-blue-700">
                                <i data-lucide="plus" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                    <div id="descriptionEditor" class="glass-input p-0 overflow-hidden" style="height: 120px;">
                        <!-- Rich text editor will be initialized here -->
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</label>
                    <input id="tags" type="text" placeholder="work, social, tools (comma-separated)" class="glass-input w-full">
                    <div id="tagSuggestions" class="hidden mt-2 flex flex-wrap gap-1">
                        <!-- Tag suggestions will appear here -->
                    </div>
                </div>

                <div class="flex justify-between items-center pt-4">
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" id="autoPreview" checked class="rounded border-gray-300">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Auto-fetch preview</span>
                        </label>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" id="openInNewTab" checked class="rounded border-gray-300">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Open in new tab</span>
                        </label>
                    </div>
                    <div class="flex space-x-3">
                        <button type="button" onclick="closeAddModal()" class="glass-button">
                            Cancel
                        </button>
                        <button type="submit" class="glass-button-primary" id="addLinkBtn">
                            <span id="addBtnText">Add Link</span>
                            <span id="addBtnLoader" class="hidden">
                                <i data-lucide="loader-2" class="w-4 h-4 animate-spin"></i>
                            </span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="modal hidden">
        <div class="modal-backdrop" onclick="closeEditModal()"></div>
        <div class="modal-content glass-card">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Edit Link</h2>
                <button onclick="closeEditModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>

            <form id="editLinkForm" class="space-y-4">
                <input type="hidden" id="editIndex">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">URL *</label>
                    <input id="editUrl" type="url" class="glass-input w-full" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Title *</label>
                    <input id="editTitle" type="text" class="glass-input w-full" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                    <textarea id="editDesc" class="glass-input w-full h-20 resize-none"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</label>
                    <input id="editTags" type="text" class="glass-input w-full">
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeEditModal()" class="glass-button">
                        Cancel
                    </button>
                    <button type="submit" class="glass-button-primary">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Category Modal -->
    <div id="categoryModal" class="modal hidden">
        <div class="modal-backdrop" onclick="closeCategoryModal()"></div>
        <div class="modal-content glass-card">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Manage Categories</h2>
                <button onclick="closeCategoryModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <form id="categoryForm" class="space-y-4 mb-6">
                <div class="flex space-x-3">
                    <input id="categoryName" type="text" placeholder="Category name" class="glass-input flex-1" required>
                    <input id="categoryColor" type="color" value="#6366f1" class="w-12 h-12 rounded-lg border-2 border-white/20">
                    <button type="submit" class="glass-button-primary">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                    </button>
                </div>
            </form>

            <div id="categoryList" class="space-y-2 max-h-60 overflow-y-auto">
                <!-- Categories will be listed here -->
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal hidden">
        <div class="modal-backdrop" onclick="closeSettingsModal()"></div>
        <div class="modal-content glass-card max-w-4xl">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Settings</h2>
                <button onclick="closeSettingsModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Appearance Settings -->
                <div class="glass-card p-4">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i data-lucide="palette" class="w-5 h-5 mr-2"></i>
                        Appearance
                    </h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">Theme</label>
                            <select id="themeSelect" class="glass-input w-full">
                                <option value="system">System</option>
                                <option value="light">Light</option>
                                <option value="dark">Dark</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Color Scheme</label>
                            <div class="grid grid-cols-3 gap-2">
                                <button class="theme-option active" data-theme="default">
                                    <div class="w-full h-8 rounded bg-gradient-to-r from-blue-500 to-purple-500"></div>
                                    <span class="text-xs mt-1">Default</span>
                                </button>
                                <button class="theme-option" data-theme="pastel">
                                    <div class="w-full h-8 rounded bg-gradient-to-r from-pink-300 to-blue-300"></div>
                                    <span class="text-xs mt-1">Pastel</span>
                                </button>
                                <button class="theme-option" data-theme="vibrant">
                                    <div class="w-full h-8 rounded bg-gradient-to-r from-red-500 to-yellow-500"></div>
                                    <span class="text-xs mt-1">Vibrant</span>
                                </button>
                                <button class="theme-option" data-theme="monochrome">
                                    <div class="w-full h-8 rounded bg-gradient-to-r from-gray-400 to-gray-600"></div>
                                    <span class="text-xs mt-1">Mono</span>
                                </button>
                                <button class="theme-option" data-theme="nature">
                                    <div class="w-full h-8 rounded bg-gradient-to-r from-green-400 to-blue-500"></div>
                                    <span class="text-xs mt-1">Nature</span>
                                </button>
                                <button class="theme-option" data-theme="sunset">
                                    <div class="w-full h-8 rounded bg-gradient-to-r from-orange-400 to-pink-500"></div>
                                    <span class="text-xs mt-1">Sunset</span>
                                </button>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Card Size</label>
                            <select id="cardSizeSelect" class="glass-input w-full">
                                <option value="small">Small</option>
                                <option value="medium" selected>Medium</option>
                                <option value="large">Large</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Behavior Settings -->
                <div class="glass-card p-4">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i data-lucide="settings" class="w-5 h-5 mr-2"></i>
                        Behavior
                    </h3>
                    <div class="space-y-4">
                        <label class="flex items-center justify-between">
                            <span class="text-sm">Auto-fetch previews</span>
                            <input type="checkbox" id="autoPreviewSetting" checked class="toggle">
                        </label>
                        <label class="flex items-center justify-between">
                            <span class="text-sm">Open links in new tab</span>
                            <input type="checkbox" id="newTabSetting" checked class="toggle">
                        </label>
                        <label class="flex items-center justify-between">
                            <span class="text-sm">Show link favicons</span>
                            <input type="checkbox" id="faviconSetting" checked class="toggle">
                        </label>
                        <label class="flex items-center justify-between">
                            <span class="text-sm">Animate card transitions</span>
                            <input type="checkbox" id="animationSetting" checked class="toggle">
                        </label>
                        <label class="flex items-center justify-between">
                            <span class="text-sm">Daily backup reminder</span>
                            <input type="checkbox" id="backupReminderSetting" class="toggle">
                        </label>
                        <div>
                            <label class="block text-sm font-medium mb-2">Default view</label>
                            <select id="defaultViewSetting" class="glass-input w-full">
                                <option value="grid">Grid</option>
                                <option value="list">List</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Data & Privacy -->
                <div class="glass-card p-4">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i data-lucide="database" class="w-5 h-5 mr-2"></i>
                        Data & Privacy
                    </h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">Storage Method</label>
                            <select id="storageMethodSetting" class="glass-input w-full">
                                <option value="localStorage">Local Storage</option>
                                <option value="firebase">Firebase (Coming Soon)</option>
                                <option value="supabase">Supabase (Coming Soon)</option>
                            </select>
                        </div>
                        <div class="space-y-2">
                            <button id="exportAllBtn" class="glass-button w-full justify-center">
                                <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                                Export All Data
                            </button>
                            <button id="clearCacheBtn" class="glass-button w-full justify-center">
                                <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                                Clear Preview Cache
                            </button>
                            <button id="resetSettingsBtn" class="glass-button w-full justify-center text-red-600">
                                <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                                Reset Settings
                            </button>
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 pt-2 border-t border-white/20">
                            <p>Storage used: <span id="storageUsed">0 KB</span></p>
                            <p>Links: <span id="totalLinks">0</span> • Categories: <span id="totalCategories">0</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-end mt-6 space-x-3">
                <button onclick="closeSettingsModal()" class="glass-button">Close</button>
                <button id="saveSettingsBtn" class="glass-button-primary">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Export Options Modal -->
    <div id="exportModal" class="modal hidden">
        <div class="modal-backdrop" onclick="closeExportModal()"></div>
        <div class="modal-content glass-card">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Export Options</h2>
                <button onclick="closeExportModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button class="export-option" data-format="json">
                    <i data-lucide="file-json" class="w-8 h-8 mb-2"></i>
                    <h3 class="font-semibold">JSON</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">For app backup & restore</p>
                </button>
                <button class="export-option" data-format="csv">
                    <i data-lucide="file-spreadsheet" class="w-8 h-8 mb-2"></i>
                    <h3 class="font-semibold">CSV</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">For spreadsheet applications</p>
                </button>
                <button class="export-option" data-format="html">
                    <i data-lucide="file-code" class="w-8 h-8 mb-2"></i>
                    <h3 class="font-semibold">HTML</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Bookmark-style webpage</p>
                </button>
                <button class="export-option" data-format="markdown">
                    <i data-lucide="file-text" class="w-8 h-8 mb-2"></i>
                    <h3 class="font-semibold">Markdown</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">For blogs & documentation</p>
                </button>
                <button class="export-option" data-format="pdf">
                    <i data-lucide="file-image" class="w-8 h-8 mb-2"></i>
                    <h3 class="font-semibold">PDF</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Visual snapshot</p>
                </button>
                <button class="export-option" data-format="share">
                    <i data-lucide="share-2" class="w-8 h-8 mb-2"></i>
                    <h3 class="font-semibold">Share Link</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Public read-only view</p>
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Modal -->
    <div id="statsModal" class="modal hidden">
        <div class="modal-backdrop" onclick="closeStatsModal()"></div>
        <div class="modal-content glass-card max-w-4xl">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Usage Statistics</h2>
                <button onclick="closeStatsModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <div id="statsContent">
                <!-- Statistics content will be populated dynamically -->
            </div>
        </div>
    </div>

    <!-- Toast Notifications Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Hidden file inputs -->
    <input type="file" id="fileInput" accept=".json,.html" class="hidden">
    <input type="file" id="imageInput" accept="image/*" class="hidden">

    <!-- Undo/Redo Floating Panel -->
    <div id="undoRedoPanel" class="hidden fixed bottom-20 right-6 glass-card p-2 flex space-x-1">
        <button id="undoBtn" class="glass-button p-2" title="Undo" disabled>
            <i data-lucide="undo" class="w-4 h-4"></i>
        </button>
        <button id="redoBtn" class="glass-button p-2" title="Redo" disabled>
            <i data-lucide="redo" class="w-4 h-4"></i>
        </button>
    </div>

    <script src="preview-service.js"></script>
    <script src="enhanced-script.js"></script>
    <script src="test-suite.js"></script>
    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Add debug console for testing
        window.debugLinkManager = () => {
            console.log('Link Manager Debug Info:');
            console.log('Links:', linkManager.links);
            console.log('Categories:', linkManager.categories);
            console.log('Settings:', linkManager.settings);
            console.log('Selected Links:', linkManager.selectedLinks);
            console.log('Current Category:', linkManager.currentCategory);
            console.log('Search Term:', linkManager.searchTerm);
            console.log('Filters:', linkManager.filters);
        };

        // Add manual test runner
        window.runTests = () => {
            const testSuite = new LinkManagerTestSuite();
            return testSuite.runAllTests();
        };
    </script>
</body>

</html>