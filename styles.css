/* Enhanced Glassmorphism Styles with Theme Support */
:root {
  /* Default Theme */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-backdrop: blur(12px);
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-color: #6366f1;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --card-size: 1;
  --animation-speed: 0.3s;
}

/* Theme Variations */
[data-theme="pastel"] {
  --primary-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  --secondary-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  --accent-color: #f472b6;
}

[data-theme="vibrant"] {
  --primary-gradient: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
  --secondary-gradient: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
  --accent-color: #ff6b6b;
}

[data-theme="monochrome"] {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
  --accent-color: #6c757d;
}

[data-theme="nature"] {
  --primary-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  --secondary-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  --accent-color: #10b981;
}

[data-theme="sunset"] {
  --primary-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --secondary-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  --accent-color: #f59e0b;
}

/* Card Size Variations */
[data-card-size="small"] {
  --card-size: 0.85;
}

[data-card-size="large"] {
  --card-size: 1.15;
}

.dark {
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

/* Enhanced Glass Card Base */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border-radius: calc(16px * var(--card-size));
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: all var(--animation-speed) cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  opacity: 0;
  transition: opacity var(--animation-speed) ease;
}

.glass-card:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 20px 60px 0 rgba(31, 38, 135, 0.6);
}

.glass-card:hover::before {
  opacity: 1;
}

/* Glass Inputs */
.glass-input {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 12px 16px;
  color: #374151;
  transition: all 0.3s ease;
  outline: none;
}

.dark .glass-input {
  color: #f3f4f6;
}

.glass-input:focus {
  border-color: rgba(99, 102, 241, 0.5);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

.glass-input::placeholder {
  color: rgba(107, 114, 128, 0.7);
}

.dark .glass-input::placeholder {
  color: rgba(156, 163, 175, 0.7);
}

/* Glass Buttons */
.glass-button {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 8px 16px;
  color: #374151;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.dark .glass-button {
  color: #f3f4f6;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.glass-button-primary {
  background: var(--primary-gradient);
  color: white;
  border: none;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.glass-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

/* Button Groups */
.glass-button-group {
  display: flex;
  border-radius: 12px;
  overflow: hidden;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
}

.view-btn {
  padding: 8px 12px;
  background: transparent;
  border: none;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  color: #374151;
}

.view-btn.active {
  background: var(--primary-gradient);
  color: white;
}

.dark .view-btn {
  color: #9ca3af;
}

.dark .view-btn:hover {
  color: #f3f4f6;
}

/* Floating Action Button */
.fab {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: var(--primary-gradient);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
}

.fab:hover {
  transform: scale(1.1) translateY(-2px);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.6);
}

.fab:active {
  transform: scale(0.95);
}

/* Modal Styles */
.modal {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.modal-backdrop {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 24px;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Link Card Styles */
.link-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: cardSlideIn 0.5s ease-out;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.link-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(31, 38, 135, 0.4);
}

.link-preview-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 12px;
}

.link-favicon {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  margin-right: 8px;
}

.tag {
  display: inline-block;
  padding: 4px 8px;
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  margin: 2px;
  transition: all 0.2s ease;
}

.tag:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: scale(1.05);
}

.dark .tag {
  background: rgba(99, 102, 241, 0.2);
  color: #a5b4fc;
}

/* View Modes */
.grid-view {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.list-view {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.list-view .link-card {
  display: flex;
  align-items: center;
  padding: 16px;
}

.list-view .link-preview-image {
  width: 80px;
  height: 60px;
  margin-right: 16px;
  margin-bottom: 0;
  flex-shrink: 0;
}

/* Utility Classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive Design */
@media (max-width: 768px) {
  .fab {
    bottom: 16px;
    right: 16px;
    width: 48px;
    height: 48px;
  }
  
  .modal-content {
    margin: 16px;
    padding: 20px;
  }
  
  .grid-view {
    grid-template-columns: 1fr;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Loading Animation */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.dark .loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

/* Category Tabs */
.category-tab {
  display: flex;
  align-items: center;
  space-x: 8px;
  padding: 8px 16px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  color: #6b7280;
  cursor: pointer;
  transition: all var(--animation-speed) ease;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 500;
}

.category-tab:hover {
  background: rgba(255, 255, 255, 0.3);
  color: #374151;
  transform: translateY(-1px);
}

.category-tab.active {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
}

.category-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
}

.category-tab.active .category-count {
  background: rgba(255, 255, 255, 0.3);
}

.dark .category-tab {
  color: #9ca3af;
}

.dark .category-tab:hover {
  color: #f3f4f6;
}

/* Enhanced Link Cards */
.link-card {
  position: relative;
  overflow: hidden;
  transition: all var(--animation-speed) cubic-bezier(0.4, 0, 0.2, 1);
  animation: cardSlideIn 0.5s ease-out;
  cursor: pointer;
  transform: scale(var(--card-size));
}

.link-card.selected {
  ring: 2px solid var(--accent-color);
  ring-opacity: 0.5;
  transform: scale(calc(var(--card-size) * 1.02));
}

.link-card .selection-checkbox {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 10;
  opacity: 0;
  transition: opacity var(--animation-speed) ease;
}

.link-card:hover .selection-checkbox,
.link-card.selected .selection-checkbox {
  opacity: 1;
}

.link-card .card-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  space-x: 4px;
  opacity: 0;
  transition: opacity var(--animation-speed) ease;
}

.link-card:hover .card-actions {
  opacity: 1;
}

/* Category Sections */
.category-section {
  margin-bottom: 32px;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: between;
  margin-bottom: 16px;
  padding: 12px 0;
  border-bottom: 1px solid var(--glass-border);
}

.category-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
}

.dark .category-title {
  color: #f3f4f6;
}

.category-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 12px;
}

.category-toggle {
  margin-left: 8px;
  transition: transform var(--animation-speed) ease;
}

.category-section.collapsed .category-toggle {
  transform: rotate(-90deg);
}

.category-section.collapsed .category-content {
  display: none;
}

/* Theme Options */
.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--animation-speed) ease;
  border: 2px solid transparent;
}

.theme-option:hover {
  background: rgba(255, 255, 255, 0.1);
}

.theme-option.active {
  border-color: var(--accent-color);
  background: rgba(99, 102, 241, 0.1);
}

/* Export Options */
.export-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  cursor: pointer;
  transition: all var(--animation-speed) ease;
  text-align: center;
}

.export-option:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Toggle Switches */
.toggle {
  appearance: none;
  width: 44px;
  height: 24px;
  background: #e5e7eb;
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  transition: background var(--animation-speed) ease;
}

.toggle:checked {
  background: var(--accent-color);
}

.toggle::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform var(--animation-speed) ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle:checked::before {
  transform: translateX(20px);
}

/* Toast Notifications */
.toast {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 16px;
  box-shadow: var(--glass-shadow);
  transform: translateX(100%);
  transition: transform var(--animation-speed) cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 400px;
  min-width: 300px;
}

.toast.show {
  transform: translateX(0);
}

.toast.success {
  border-left: 4px solid var(--success-color);
}

.toast.error {
  border-left: 4px solid var(--error-color);
}

.toast.warning {
  border-left: 4px solid var(--warning-color);
}

.toast.info {
  border-left: 4px solid var(--accent-color);
}
