// Link Preview Service
class LinkPreviewService {
    constructor() {
        this.cache = new Map();
        this.apiKey = null; // Will use free tier
        this.baseUrl = 'https://api.microlink.io';
    }

    // Get cached preview or fetch new one
    async getPreview(url) {
        // Check cache first
        const cached = this.getCachedPreview(url);
        if (cached) {
            return cached;
        }

        try {
            const preview = await this.fetchPreview(url);
            this.cachePreview(url, preview);
            return preview;
        } catch (error) {
            console.warn('Failed to fetch preview for:', url, error);
            return this.getFallbackPreview(url);
        }
    }

    // Fetch preview from Microlink API
    async fetchPreview(url) {
        const apiUrl = `${this.baseUrl}?url=${encodeURIComponent(url)}&screenshot=false&video=false`;
        
        const response = await fetch(apiUrl);
        if (!response.ok) {
            throw new Error(`API request failed: ${response.status}`);
        }

        const data = await response.json();
        
        if (!data.status === 'success') {
            throw new Error('API returned error status');
        }

        return this.formatPreviewData(data.data, url);
    }

    // Format API response to our standard format
    formatPreviewData(data, url) {
        const domain = new URL(url).hostname;
        
        return {
            title: data.title || domain,
            description: data.description || '',
            image: data.image?.url || data.logo?.url || this.getFaviconUrl(url),
            domain: domain,
            url: url,
            timestamp: Date.now()
        };
    }

    // Get favicon URL
    getFaviconUrl(url) {
        try {
            const domain = new URL(url).origin;
            return `${domain}/favicon.ico`;
        } catch {
            return 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M13.5 2c-5.621 0-10.211 4.443-10.475 10h-3.025l4 5.917 4-5.917h-2.938c.244-4.058 3.583-7.312 7.438-7.312 4.106 0 7.438 3.332 7.438 7.438s-3.332 7.438-7.438 7.438c-1.287 0-2.513-.328-3.563-.906l-1.706 2.938c1.538.894 3.31 1.406 5.269 1.406 5.621 0 10.211-4.443 10.475-10h3.025l-4-5.917-4 5.917h2.938c-.244 4.058-3.583 7.312-7.438 7.312z"/></svg>';
        }
    }

    // Get fallback preview when API fails
    getFallbackPreview(url) {
        try {
            const urlObj = new URL(url);
            const domain = urlObj.hostname;
            
            return {
                title: this.extractTitleFromUrl(url) || domain,
                description: `Link from ${domain}`,
                image: this.getFaviconUrl(url),
                domain: domain,
                url: url,
                timestamp: Date.now(),
                fallback: true
            };
        } catch {
            return {
                title: 'Invalid URL',
                description: 'Unable to parse URL',
                image: this.getFaviconUrl('https://example.com'),
                domain: 'unknown',
                url: url,
                timestamp: Date.now(),
                fallback: true
            };
        }
    }

    // Extract title from URL path
    extractTitleFromUrl(url) {
        try {
            const urlObj = new URL(url);
            const path = urlObj.pathname;
            
            // Remove leading slash and file extensions
            let title = path.replace(/^\//, '').replace(/\.[^/.]+$/, '');
            
            // Replace hyphens and underscores with spaces
            title = title.replace(/[-_]/g, ' ');
            
            // Capitalize words
            title = title.replace(/\b\w/g, l => l.toUpperCase());
            
            return title || null;
        } catch {
            return null;
        }
    }

    // Cache management
    getCachedPreview(url) {
        const cached = this.cache.get(url);
        if (!cached) return null;

        // Check if cache is still valid (24 hours)
        const isExpired = Date.now() - cached.timestamp > 24 * 60 * 60 * 1000;
        if (isExpired) {
            this.cache.delete(url);
            return null;
        }

        return cached;
    }

    cachePreview(url, preview) {
        this.cache.set(url, preview);
        
        // Also save to localStorage for persistence
        try {
            const cacheData = JSON.parse(localStorage.getItem('linkPreviews') || '{}');
            cacheData[url] = preview;
            
            // Limit cache size (keep only 100 most recent)
            const entries = Object.entries(cacheData);
            if (entries.length > 100) {
                entries.sort((a, b) => b[1].timestamp - a[1].timestamp);
                const limitedCache = Object.fromEntries(entries.slice(0, 100));
                localStorage.setItem('linkPreviews', JSON.stringify(limitedCache));
            } else {
                localStorage.setItem('linkPreviews', JSON.stringify(cacheData));
            }
        } catch (error) {
            console.warn('Failed to save preview to localStorage:', error);
        }
    }

    // Load cache from localStorage
    loadCacheFromStorage() {
        try {
            const cacheData = JSON.parse(localStorage.getItem('linkPreviews') || '{}');
            Object.entries(cacheData).forEach(([url, preview]) => {
                // Only load if not expired
                const isExpired = Date.now() - preview.timestamp > 24 * 60 * 60 * 1000;
                if (!isExpired) {
                    this.cache.set(url, preview);
                }
            });
        } catch (error) {
            console.warn('Failed to load preview cache from localStorage:', error);
        }
    }

    // Clear cache
    clearCache() {
        this.cache.clear();
        try {
            localStorage.removeItem('linkPreviews');
        } catch (error) {
            console.warn('Failed to clear preview cache from localStorage:', error);
        }
    }

    // Get domain-specific handling
    getDomainSpecificPreview(url) {
        try {
            const urlObj = new URL(url);
            const domain = urlObj.hostname.toLowerCase();

            // YouTube
            if (domain.includes('youtube.com') || domain.includes('youtu.be')) {
                return this.getYouTubePreview(url);
            }

            // Twitter/X
            if (domain.includes('twitter.com') || domain.includes('x.com')) {
                return this.getTwitterPreview(url);
            }

            // GitHub
            if (domain.includes('github.com')) {
                return this.getGitHubPreview(url);
            }

            return null;
        } catch {
            return null;
        }
    }

    // YouTube-specific preview
    getYouTubePreview(url) {
        try {
            const videoId = this.extractYouTubeId(url);
            if (!videoId) return null;

            return {
                title: 'YouTube Video',
                description: 'Click to watch on YouTube',
                image: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
                domain: 'youtube.com',
                url: url,
                timestamp: Date.now(),
                type: 'youtube'
            };
        } catch {
            return null;
        }
    }

    extractYouTubeId(url) {
        const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
        const match = url.match(regex);
        return match ? match[1] : null;
    }

    // Initialize service
    init() {
        this.loadCacheFromStorage();
    }
}

// Create global instance
window.linkPreviewService = new LinkPreviewService();
window.linkPreviewService.init();
