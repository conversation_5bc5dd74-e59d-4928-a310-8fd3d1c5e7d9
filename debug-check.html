<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Check - Link Manager Pro</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #f5f5f5; }
        .debug-panel { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Link Manager Pro - Debug Check</h1>
    
    <div class="debug-panel">
        <h2>Dependency Check</h2>
        <div id="dependencyCheck"></div>
    </div>
    
    <div class="debug-panel">
        <h2>Element Check</h2>
        <div id="elementCheck"></div>
    </div>
    
    <div class="debug-panel">
        <h2>JavaScript Errors</h2>
        <div id="errorLog"></div>
    </div>
    
    <div class="debug-panel">
        <h2>Quick Test</h2>
        <button onclick="runQuickTest()">Run Quick Test</button>
        <div id="testResults"></div>
    </div>

    <script>
        // Capture JavaScript errors
        const errors = [];
        window.addEventListener('error', (e) => {
            errors.push({
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                error: e.error
            });
            updateErrorLog();
        });

        function log(message, type = 'info') {
            console.log(`[${type.toUpperCase()}] ${message}`);
            return `<div class="${type}">[${type.toUpperCase()}] ${message}</div>`;
        }

        function checkDependencies() {
            let output = '';
            
            // Check external libraries
            output += typeof Quill !== 'undefined' ? log('✓ Quill.js loaded', 'success') : log('✗ Quill.js not loaded', 'error');
            output += typeof Sortable !== 'undefined' ? log('✓ SortableJS loaded', 'success') : log('✗ SortableJS not loaded', 'error');
            output += typeof lucide !== 'undefined' ? log('✓ Lucide icons loaded', 'success') : log('✗ Lucide icons not loaded', 'error');
            output += typeof jsPDF !== 'undefined' ? log('✓ jsPDF loaded', 'success') : log('✗ jsPDF not loaded', 'warning');
            
            // Check if main app is loaded
            output += typeof linkManager !== 'undefined' ? log('✓ Link Manager initialized', 'success') : log('✗ Link Manager not initialized', 'error');
            
            document.getElementById('dependencyCheck').innerHTML = output;
        }

        function checkElements() {
            let output = '';
            
            const requiredElements = [
                'addCategoryBtn', 'categoryForm', 'searchInput', 'clearSearchBtn',
                'tagFilter', 'domainFilter', 'sortFilter', 'filterToggleBtn',
                'selectAllBtn', 'settingsBtn', 'gridViewBtn', 'listViewBtn',
                'addLinkForm', 'editLinkForm', 'url', 'title', 'tags',
                'linkContainer', 'categoryContainer', 'addModal', 'editModal',
                'settingsModal', 'exportModal', 'categoryModal'
            ];
            
            requiredElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    output += log(`✓ Element '${id}' found`, 'success');
                } else {
                    output += log(`✗ Element '${id}' missing`, 'error');
                }
            });
            
            document.getElementById('elementCheck').innerHTML = output;
        }

        function updateErrorLog() {
            let output = '';
            if (errors.length === 0) {
                output = log('No JavaScript errors detected', 'success');
            } else {
                errors.forEach((error, index) => {
                    output += log(`Error ${index + 1}: ${error.message} (${error.filename}:${error.lineno}:${error.colno})`, 'error');
                });
            }
            document.getElementById('errorLog').innerHTML = output;
        }

        function runQuickTest() {
            let output = '';
            
            try {
                // Test basic functionality
                if (typeof linkManager !== 'undefined') {
                    output += log('✓ Link Manager accessible', 'success');
                    
                    // Test methods exist
                    const methods = ['openAddModal', 'closeAddModal', 'renderLinks', 'saveLinks'];
                    methods.forEach(method => {
                        if (typeof linkManager[method] === 'function') {
                            output += log(`✓ Method '${method}' exists`, 'success');
                        } else {
                            output += log(`✗ Method '${method}' missing`, 'error');
                        }
                    });
                    
                    // Test data structures
                    output += Array.isArray(linkManager.links) ? log('✓ Links array initialized', 'success') : log('✗ Links array not initialized', 'error');
                    output += Array.isArray(linkManager.categories) ? log('✓ Categories array initialized', 'success') : log('✗ Categories array not initialized', 'error');
                    output += typeof linkManager.settings === 'object' ? log('✓ Settings object initialized', 'success') : log('✗ Settings object not initialized', 'error');
                    
                } else {
                    output += log('✗ Link Manager not accessible', 'error');
                }
                
            } catch (error) {
                output += log(`Test failed: ${error.message}`, 'error');
            }
            
            document.getElementById('testResults').innerHTML = output;
        }

        // Run checks when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkDependencies();
                checkElements();
                updateErrorLog();
            }, 1000);
        });
    </script>
    
    <!-- Load the same dependencies as main app -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.snow.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <!-- Load app scripts -->
    <script src="preview-service.js"></script>
    <script src="enhanced-script.js"></script>
    
    <script>
        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    </script>
</body>
</html>
