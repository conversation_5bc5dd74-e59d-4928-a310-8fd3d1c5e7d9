// Enhanced Link Manager Pro with Advanced Features
class EnhancedLinkManager {
    constructor() {
        this.links = JSON.parse(localStorage.getItem('links')) || [];
        this.categories = JSON.parse(localStorage.getItem('categories')) || this.getDefaultCategories();
        this.settings = JSON.parse(localStorage.getItem('settings')) || this.getDefaultSettings();
        this.selectedLinks = new Set();
        this.currentCategory = '';
        this.currentView = this.settings.defaultView || 'grid';
        this.searchTerm = '';
        this.filters = {
            tag: '',
            domain: '',
            sort: 'dateAdded',
            dateFrom: '',
            dateTo: '',
            type: '',
            hasPreview: ''
        };
        this.undoStack = [];
        this.redoStack = [];
        this.maxUndoSteps = 50;
        this.quillEditor = null;
        this.sortableInstance = null;
        
        this.init();
    }

    getDefaultCategories() {
        return [
            { id: 'work', name: 'Work', color: '#3b82f6', created: Date.now() },
            { id: 'personal', name: 'Personal', color: '#10b981', created: Date.now() },
            { id: 'social', name: 'Social', color: '#f59e0b', created: Date.now() },
            { id: 'tech', name: 'Tech', color: '#8b5cf6', created: Date.now() }
        ];
    }

    getDefaultSettings() {
        return {
            theme: 'system',
            colorScheme: 'default',
            cardSize: 'medium',
            autoPreview: true,
            newTab: true,
            showFavicons: true,
            animations: true,
            backupReminder: false,
            defaultView: 'grid',
            storageMethod: 'localStorage'
        };
    }

    init() {
        this.applySettings();
        this.setupEventListeners();
        this.setupRichTextEditor();
        this.renderCategories();
        this.renderLinks();
        this.updateStats();
        this.setupKeyboardShortcuts();
        this.checkBackupReminder();
    }

    applySettings() {
        // Apply theme
        document.documentElement.setAttribute('data-theme', this.settings.colorScheme);
        document.documentElement.setAttribute('data-card-size', this.settings.cardSize);
        
        if (this.settings.theme === 'dark' || 
            (this.settings.theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        }

        // Apply animation settings
        if (!this.settings.animations) {
            document.documentElement.style.setProperty('--animation-speed', '0s');
        }
    }

    setupEventListeners() {
        // Helper function to safely add event listeners
        const safeAddEventListener = (id, event, handler) => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener(event, handler);
            } else {
                console.warn(`Element with id '${id}' not found`);
            }
        };

        // Category management
        safeAddEventListener('addCategoryBtn', 'click', () => this.openCategoryModal());
        safeAddEventListener('categoryForm', 'submit', (e) => this.handleAddCategory(e));

        // Enhanced search and filters
        safeAddEventListener('searchInput', 'input', (e) => this.handleSearch(e));
        safeAddEventListener('clearSearchBtn', 'click', () => this.clearSearch());
        safeAddEventListener('tagFilter', 'change', (e) => this.handleFilterChange('tag', e.target.value));
        safeAddEventListener('domainFilter', 'change', (e) => this.handleFilterChange('domain', e.target.value));
        safeAddEventListener('sortFilter', 'change', (e) => this.handleFilterChange('sort', e.target.value));
        
        // Advanced filters
        safeAddEventListener('filterToggleBtn', 'click', () => this.toggleAdvancedFilters());
        safeAddEventListener('applyFiltersBtn', 'click', () => this.applyAdvancedFilters());
        safeAddEventListener('clearFiltersBtn', 'click', () => this.clearAllFilters());

        // Bulk operations
        safeAddEventListener('selectAllBtn', 'click', () => this.selectAllVisible());
        safeAddEventListener('bulkDeleteBtn', 'click', () => this.bulkDelete());
        safeAddEventListener('bulkMoveBtn', 'click', () => this.bulkMove());
        safeAddEventListener('bulkExportBtn', 'click', () => this.bulkExport());
        safeAddEventListener('clearSelectionBtn', 'click', () => this.clearSelection());

        // Settings
        safeAddEventListener('settingsBtn', 'click', () => this.openSettingsModal());
        safeAddEventListener('saveSettingsBtn', 'click', () => this.saveSettings());

        // Export/Import
        safeAddEventListener('exportMenuBtn', 'click', () => this.openExportModal());
        safeAddEventListener('importMenuBtn', 'click', () => this.openImportModal());

        // Undo/Redo
        safeAddEventListener('undoBtn', 'click', () => this.undo());
        safeAddEventListener('redoBtn', 'click', () => this.redo());

        // View toggles
        safeAddEventListener('gridViewBtn', 'click', () => this.setViewMode('grid'));
        safeAddEventListener('listViewBtn', 'click', () => this.setViewMode('list'));

        // Form submissions
        safeAddEventListener('addLinkForm', 'submit', (e) => this.handleAddLink(e));
        safeAddEventListener('editLinkForm', 'submit', (e) => this.handleEditLink(e));

        // URL preview with debouncing
        safeAddEventListener('url', 'input', this.debounce((e) => this.handleUrlPreview(e), 500));

        // Custom image upload
        safeAddEventListener('customImageBtn', 'click', () => this.openImageUpload());
        safeAddEventListener('imageInput', 'change', (e) => this.handleImageUpload(e));

        // File import
        safeAddEventListener('fileInput', 'change', (e) => this.handleFileImport(e));

        // Statistics
        safeAddEventListener('statsBtn', 'click', () => this.openStatsModal());
        
        // Theme selection
        document.querySelectorAll('.theme-option').forEach(btn => {
            btn.addEventListener('click', (e) => this.selectTheme(e.target.closest('.theme-option').dataset.theme));
        });
        
        // Export format selection
        document.addEventListener('click', (e) => {
            if (e.target.closest('.export-option')) {
                const format = e.target.closest('.export-option').dataset.format;
                this.exportData(format);
            }
        });
        
        // Close modals on escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('#menuToggle') && !e.target.closest('#dropdownMenu')) {
                document.getElementById('dropdownMenu').classList.add('hidden');
            }
        });
    }

    setupRichTextEditor() {
        try {
            const editorElement = document.getElementById('descriptionEditor');
            if (!editorElement) {
                console.warn('Description editor element not found');
                return;
            }

            const toolbarOptions = [
                ['bold', 'italic', 'underline'],
                ['link'],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                ['clean']
            ];

            if (typeof Quill !== 'undefined') {
                this.quillEditor = new Quill('#descriptionEditor', {
                    theme: 'snow',
                    modules: {
                        toolbar: toolbarOptions
                    },
                    placeholder: 'Enter a description...'
                });
            } else {
                console.warn('Quill editor not available, falling back to textarea');
                editorElement.innerHTML = '<textarea id="descriptionTextarea" class="w-full h-full border-none outline-none resize-none" placeholder="Enter a description..."></textarea>';
            }
        } catch (error) {
            console.error('Failed to setup rich text editor:', error);
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + N: New link
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                this.openAddModal();
            }
            
            // Ctrl/Cmd + Z: Undo
            if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
                e.preventDefault();
                this.undo();
            }
            
            // Ctrl/Cmd + Shift + Z: Redo
            if ((e.ctrlKey || e.metaKey) && e.key === 'z' && e.shiftKey) {
                e.preventDefault();
                this.redo();
            }
            
            // Ctrl/Cmd + A: Select all
            if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
                e.preventDefault();
                this.selectAllVisible();
            }
            
            // Delete: Delete selected
            if (e.key === 'Delete' && this.selectedLinks.size > 0) {
                e.preventDefault();
                this.bulkDelete();
            }
            
            // Ctrl/Cmd + F: Focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            }
        });
    }

    // Category Management
    renderCategories() {
        const tabsContainer = document.getElementById('categoryTabs');
        const allBtn = document.getElementById('allCategoriesBtn');
        const categorySelect = document.getElementById('categories');

        if (!tabsContainer || !allBtn) {
            console.warn('Category UI elements not found');
            return;
        }

        // Update all links count
        const allCount = allBtn.querySelector('.category-count');
        if (allCount) {
            allCount.textContent = this.links.length;
        }

        // Clear existing tabs
        tabsContainer.innerHTML = '';
        if (categorySelect) {
            categorySelect.innerHTML = '';
        }

        this.categories.forEach(category => {
            // Create tab
            const tab = document.createElement('button');
            tab.className = 'category-tab';
            tab.dataset.category = category.id;
            tab.innerHTML = `
                <div class="category-color" style="background-color: ${category.color}"></div>
                <span>${category.name}</span>
                <span class="category-count">${this.getLinkCountForCategory(category.id)}</span>
            `;
            tab.addEventListener('click', () => this.selectCategory(category.id));
            tabsContainer.appendChild(tab);

            // Add to select dropdown if it exists
            if (categorySelect) {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            }
        });

        this.updateCategorySelection();
    }

    getLinkCountForCategory(categoryId) {
        return this.links.filter(link => 
            link.categories && link.categories.includes(categoryId)
        ).length;
    }

    selectCategory(categoryId) {
        this.currentCategory = categoryId;
        this.updateCategorySelection();
        this.renderLinks();
    }

    updateCategorySelection() {
        // Update category tabs
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.category === this.currentCategory);
        });

        // Update "All Categories" button
        const allBtn = document.getElementById('allCategoriesBtn');
        if (allBtn) {
            allBtn.classList.toggle('active', this.currentCategory === '');
        }
    }

    openCategoryModal() {
        document.getElementById('categoryModal').classList.remove('hidden');
        this.renderCategoryList();
    }

    closeCategoryModal() {
        document.getElementById('categoryModal').classList.add('hidden');
    }

    handleAddCategory(e) {
        e.preventDefault();
        const name = document.getElementById('categoryName').value.trim();
        const color = document.getElementById('categoryColor').value;
        
        if (!name) return;
        
        const category = {
            id: this.generateId(),
            name,
            color,
            created: Date.now()
        };
        
        this.saveState();
        this.categories.push(category);
        this.saveCategories();
        this.renderCategories();
        this.renderCategoryList();
        
        // Clear form
        document.getElementById('categoryName').value = '';
        document.getElementById('categoryColor').value = '#6366f1';
        
        this.showToast(`Category "${name}" created successfully!`, 'success');
    }

    renderCategoryList() {
        const container = document.getElementById('categoryList');
        container.innerHTML = '';
        
        this.categories.forEach(category => {
            const item = document.createElement('div');
            item.className = 'flex items-center justify-between p-3 glass-card mb-2';
            item.innerHTML = `
                <div class="flex items-center space-x-3">
                    <div class="category-color" style="background-color: ${category.color}"></div>
                    <span class="font-medium">${category.name}</span>
                    <span class="text-sm text-gray-500">(${this.getLinkCountForCategory(category.id)} links)</span>
                </div>
                <div class="flex space-x-2">
                    <button onclick="linkManager.editCategory('${category.id}')" class="glass-button p-1">
                        <i data-lucide="edit-2" class="w-4 h-4"></i>
                    </button>
                    <button onclick="linkManager.deleteCategory('${category.id}')" class="glass-button p-1 text-red-600">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                    </button>
                </div>
            `;
            container.appendChild(item);
        });
        
        // Re-initialize Lucide icons
        lucide.createIcons();
    }

    deleteCategory(categoryId) {
        const category = this.categories.find(c => c.id === categoryId);
        if (!category) return;
        
        if (confirm(`Delete category "${category.name}"? Links in this category will not be deleted.`)) {
            this.saveState();
            this.categories = this.categories.filter(c => c.id !== categoryId);
            
            // Remove category from all links
            this.links.forEach(link => {
                if (link.categories) {
                    link.categories = link.categories.filter(c => c !== categoryId);
                }
            });
            
            this.saveCategories();
            this.saveLinks();
            this.renderCategories();
            this.renderCategoryList();
            this.renderLinks();
            
            this.showToast(`Category "${category.name}" deleted successfully!`, 'success');
        }
    }

    // Enhanced Link Management
    async handleAddLink(e) {
        e.preventDefault();

        const url = document.getElementById('url').value.trim();
        const title = document.getElementById('title').value.trim();

        // Get description from Quill editor or fallback textarea
        let description = '';
        if (this.quillEditor) {
            description = this.quillEditor.getContents();
        } else {
            const textarea = document.getElementById('descriptionTextarea');
            description = textarea ? textarea.value : '';
        }

        const tags = document.getElementById('tags').value.trim();
        const categoriesSelect = document.getElementById('categories');
        const categories = categoriesSelect ? Array.from(categoriesSelect.selectedOptions).map(opt => opt.value) : [];
        
        if (!url || !title) {
            this.showToast('URL and title are required', 'error');
            return;
        }

        if (!this.isValidUrl(url)) {
            this.showToast('Please enter a valid URL', 'error');
            return;
        }

        this.setLoadingState(true);

        try {
            const preview = await window.linkPreviewService.getPreview(url);
            
            const newLink = {
                id: this.generateId(),
                url: url,
                title: title,
                description: description,
                tags: tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
                categories: categories,
                preview: preview,
                dateAdded: new Date().toISOString(),
                lastVisited: null,
                visitCount: 0,
                order: this.links.length,
                customImage: null
            };

            this.saveState();
            this.links.push(newLink);
            this.saveLinks();
            this.renderLinks();
            this.updateStats();
            this.updateFilters();
            this.closeAddModal();
            this.resetAddForm();
            
            this.showToast('Link added successfully!', 'success');
        } catch (error) {
            console.error('Failed to add link:', error);
            this.showToast('Failed to add link. Please try again.', 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    // Utility Methods
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    saveState() {
        const state = {
            links: JSON.parse(JSON.stringify(this.links)),
            categories: JSON.parse(JSON.stringify(this.categories)),
            timestamp: Date.now()
        };
        
        this.undoStack.push(state);
        if (this.undoStack.length > this.maxUndoSteps) {
            this.undoStack.shift();
        }
        this.redoStack = [];
        this.updateUndoRedoButtons();
    }

    undo() {
        if (this.undoStack.length === 0) return;
        
        const currentState = {
            links: JSON.parse(JSON.stringify(this.links)),
            categories: JSON.parse(JSON.stringify(this.categories)),
            timestamp: Date.now()
        };
        
        this.redoStack.push(currentState);
        const previousState = this.undoStack.pop();
        
        this.links = previousState.links;
        this.categories = previousState.categories;
        
        this.saveLinks();
        this.saveCategories();
        this.renderCategories();
        this.renderLinks();
        this.updateStats();
        this.updateUndoRedoButtons();
        
        this.showToast('Action undone', 'info');
    }

    redo() {
        if (this.redoStack.length === 0) return;
        
        const currentState = {
            links: JSON.parse(JSON.stringify(this.links)),
            categories: JSON.parse(JSON.stringify(this.categories)),
            timestamp: Date.now()
        };
        
        this.undoStack.push(currentState);
        const nextState = this.redoStack.pop();
        
        this.links = nextState.links;
        this.categories = nextState.categories;
        
        this.saveLinks();
        this.saveCategories();
        this.renderCategories();
        this.renderLinks();
        this.updateStats();
        this.updateUndoRedoButtons();
        
        this.showToast('Action redone', 'info');
    }

    updateUndoRedoButtons() {
        const undoBtn = document.getElementById('undoBtn');
        const redoBtn = document.getElementById('redoBtn');
        const panel = document.getElementById('undoRedoPanel');
        
        undoBtn.disabled = this.undoStack.length === 0;
        redoBtn.disabled = this.redoStack.length === 0;
        
        // Show/hide panel based on availability
        if (this.undoStack.length > 0 || this.redoStack.length > 0) {
            panel.classList.remove('hidden');
        } else {
            panel.classList.add('hidden');
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    saveLinks() {
        localStorage.setItem('links', JSON.stringify(this.links));
    }

    saveCategories() {
        localStorage.setItem('categories', JSON.stringify(this.categories));
    }

    saveSettings() {
        localStorage.setItem('settings', JSON.stringify(this.settings));
    }

    showToast(message, type = 'info', duration = 3000) {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const icon = {
            success: 'check-circle',
            error: 'x-circle',
            warning: 'alert-triangle',
            info: 'info'
        }[type] || 'info';
        
        toast.innerHTML = `
            <div class="flex items-center space-x-3">
                <i data-lucide="${icon}" class="w-5 h-5 flex-shrink-0"></i>
                <p class="text-sm font-medium text-gray-800 dark:text-white">${message}</p>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-auto text-gray-500 hover:text-gray-700">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
        `;
        
        container.appendChild(toast);
        lucide.createIcons();
        
        // Animate in
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Auto remove
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    updateStats() {
        document.getElementById('linkCount').textContent = `${this.links.length} links`;
        document.getElementById('categoryCount').textContent = `${this.categories.length} categories`;
    }

    closeAllModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.add('hidden');
        });
    }

    // Search and Filter Implementation
    handleSearch(e) {
        this.searchTerm = e.target.value.toLowerCase();
        const clearBtn = document.getElementById('clearSearchBtn');

        if (this.searchTerm) {
            clearBtn.classList.remove('hidden');
        } else {
            clearBtn.classList.add('hidden');
        }

        this.renderLinks();
    }

    clearSearch() {
        document.getElementById('searchInput').value = '';
        this.searchTerm = '';
        document.getElementById('clearSearchBtn').classList.add('hidden');
        this.renderLinks();
    }

    handleFilterChange(type, value) {
        this.filters[type] = value;
        this.renderLinks();
    }

    toggleAdvancedFilters() {
        const panel = document.getElementById('advancedFilters');
        panel.classList.toggle('hidden');
    }

    applyAdvancedFilters() {
        this.filters.dateFrom = document.getElementById('dateFrom').value;
        this.filters.dateTo = document.getElementById('dateTo').value;
        this.filters.type = document.getElementById('typeFilter').value;
        this.filters.hasPreview = document.getElementById('previewFilter').value;
        this.renderLinks();
        this.toggleAdvancedFilters();
    }

    clearAllFilters() {
        this.filters = {
            tag: '',
            domain: '',
            sort: 'dateAdded',
            dateFrom: '',
            dateTo: '',
            type: '',
            hasPreview: ''
        };

        // Reset form elements
        document.getElementById('tagFilter').value = '';
        document.getElementById('domainFilter').value = '';
        document.getElementById('sortFilter').value = 'dateAdded';
        document.getElementById('dateFrom').value = '';
        document.getElementById('dateTo').value = '';
        document.getElementById('typeFilter').value = '';
        document.getElementById('previewFilter').value = '';

        this.renderLinks();
        this.toggleAdvancedFilters();
    }

    // Bulk Operations
    selectAllVisible() {
        const visibleLinks = this.getFilteredLinks();
        visibleLinks.forEach(link => this.selectedLinks.add(link.id));
        this.updateBulkActions();
        this.renderLinks();
    }

    clearSelection() {
        this.selectedLinks.clear();
        this.updateBulkActions();
        this.renderLinks();
    }

    updateBulkActions() {
        const bulkActions = document.getElementById('bulkActions');
        const selectedCount = document.getElementById('selectedCount');

        if (this.selectedLinks.size > 0) {
            bulkActions.classList.remove('hidden');
            selectedCount.textContent = `${this.selectedLinks.size} selected`;
        } else {
            bulkActions.classList.add('hidden');
        }
    }

    bulkDelete() {
        if (this.selectedLinks.size === 0) return;

        if (confirm(`Delete ${this.selectedLinks.size} selected links?`)) {
            this.saveState();
            this.links = this.links.filter(link => !this.selectedLinks.has(link.id));
            this.selectedLinks.clear();
            this.saveLinks();
            this.renderLinks();
            this.updateStats();
            this.updateBulkActions();
            this.showToast(`${this.selectedLinks.size} links deleted`, 'success');
        }
    }

    bulkMove() {
        if (this.selectedLinks.size === 0) return;

        // Show category selection modal
        this.showBulkMoveModal();
    }

    bulkExport() {
        if (this.selectedLinks.size === 0) return;

        const selectedLinks = this.links.filter(link => this.selectedLinks.has(link.id));
        this.exportSelectedLinks(selectedLinks);
    }

    // Modal Management
    openAddModal() {
        document.getElementById('addModal').classList.remove('hidden');
        document.getElementById('url').focus();
        this.populateCategoryOptions();
    }

    closeAddModal() {
        document.getElementById('addModal').classList.add('hidden');
        this.resetAddForm();
    }

    resetAddForm() {
        const form = document.getElementById('addLinkForm');
        if (form) form.reset();

        // Reset Quill editor or textarea
        if (this.quillEditor) {
            this.quillEditor.setContents([]);
        } else {
            const textarea = document.getElementById('descriptionTextarea');
            if (textarea) textarea.value = '';
        }

        const urlPreview = document.getElementById('urlPreview');
        if (urlPreview) urlPreview.classList.add('hidden');
    }

    openSettingsModal() {
        document.getElementById('settingsModal').classList.remove('hidden');
        this.populateSettingsForm();
    }

    closeSettingsModal() {
        document.getElementById('settingsModal').classList.add('hidden');
    }

    openExportModal() {
        document.getElementById('exportModal').classList.remove('hidden');
        document.getElementById('dropdownMenu').classList.add('hidden');
    }

    closeExportModal() {
        document.getElementById('exportModal').classList.add('hidden');
    }

    openStatsModal() {
        document.getElementById('statsModal').classList.remove('hidden');
        this.renderStatistics();
        document.getElementById('dropdownMenu').classList.add('hidden');
    }

    closeStatsModal() {
        document.getElementById('statsModal').classList.add('hidden');
    }

    // View Management
    setViewMode(mode) {
        this.currentView = mode;
        this.settings.defaultView = mode;
        this.saveSettings();

        const container = document.getElementById('linkContainer');
        const gridBtn = document.getElementById('gridViewBtn');
        const listBtn = document.getElementById('listViewBtn');

        if (mode === 'grid') {
            container.className = 'grid-view grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
            gridBtn.classList.add('active');
            listBtn.classList.remove('active');
        } else {
            container.className = 'list-view flex flex-col gap-4';
            listBtn.classList.add('active');
            gridBtn.classList.remove('active');
        }
    }

    // Link Preview and Image Handling
    async handleUrlPreview(e) {
        const url = e.target.value.trim();
        const previewDiv = document.getElementById('urlPreview');

        if (!url || !this.isValidUrl(url)) {
            previewDiv.classList.add('hidden');
            return;
        }

        if (!this.settings.autoPreview) return;

        try {
            const preview = await window.linkPreviewService.getPreview(url);
            this.displayUrlPreview(preview);
        } catch (error) {
            console.warn('Preview failed:', error);
            previewDiv.classList.add('hidden');
        }
    }

    displayUrlPreview(preview) {
        const previewDiv = document.getElementById('urlPreview');
        const titleInput = document.getElementById('title');

        document.getElementById('previewImage').src = preview.image;
        document.getElementById('previewTitle').textContent = preview.title;
        document.getElementById('previewDescription').textContent = preview.description;
        document.getElementById('previewDomain').textContent = preview.domain;

        // Auto-fill form if empty
        if (!titleInput.value) titleInput.value = preview.title;

        previewDiv.classList.remove('hidden');
    }

    openImageUpload() {
        document.getElementById('imageInput').click();
    }

    handleImageUpload(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            document.getElementById('previewImage').src = e.target.result;
            // Store custom image data
            this.customImageData = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    setLoadingState(loading) {
        const btn = document.getElementById('addLinkBtn');
        const text = document.getElementById('addBtnText');
        const loader = document.getElementById('addBtnLoader');

        if (loading) {
            btn.disabled = true;
            text.classList.add('hidden');
            loader.classList.remove('hidden');
        } else {
            btn.disabled = false;
            text.classList.remove('hidden');
            loader.classList.add('hidden');
        }
    }

    // Link Rendering
    renderLinks() {
        const container = document.getElementById('linkContainer');
        const categoryContainer = document.getElementById('categoryContainer');
        const emptyState = document.getElementById('emptyState');
        const categoryEmptyState = document.getElementById('categoryEmptyState');

        const filteredLinks = this.getFilteredLinks();

        // Clear containers
        container.innerHTML = '';
        categoryContainer.innerHTML = '';

        if (filteredLinks.length === 0) {
            container.style.display = 'none';
            categoryContainer.style.display = 'none';
            if (this.currentCategory) {
                categoryEmptyState.classList.remove('hidden');
                emptyState.classList.add('hidden');
            } else {
                emptyState.classList.remove('hidden');
                categoryEmptyState.classList.add('hidden');
            }
            return;
        }

        emptyState.classList.add('hidden');
        categoryEmptyState.classList.add('hidden');

        if (this.currentCategory) {
            // Show only current category
            container.style.display = 'grid';
            categoryContainer.style.display = 'none';
            this.renderLinksInContainer(filteredLinks, container);
        } else {
            // Show all categories
            container.style.display = 'none';
            categoryContainer.style.display = 'block';
            this.renderCategorizedLinks(filteredLinks);
        }

        this.setupSortable();
    }

    renderCategorizedLinks(links) {
        const container = document.getElementById('categoryContainer');

        // Group links by category
        const linksByCategory = this.groupLinksByCategory(links);

        // Render each category section
        Object.entries(linksByCategory).forEach(([categoryId, categoryLinks]) => {
            const category = this.categories.find(c => c.id === categoryId) || { id: 'uncategorized', name: 'Uncategorized', color: '#6b7280' };

            const section = document.createElement('div');
            section.className = 'category-section';
            section.innerHTML = `
                <div class="category-header">
                    <div class="category-title" onclick="linkManager.toggleCategorySection('${categoryId}')">
                        <div class="category-color" style="background-color: ${category.color}"></div>
                        <span>${category.name}</span>
                        <span class="text-sm text-gray-500 ml-2">(${categoryLinks.length})</span>
                        <i data-lucide="chevron-down" class="category-toggle w-4 h-4 ml-2"></i>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="linkManager.selectCategory('${categoryId}')" class="glass-button text-sm">
                            View All
                        </button>
                    </div>
                </div>
                <div class="category-content">
                    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" id="category-${categoryId}">
                    </div>
                </div>
            `;

            container.appendChild(section);

            const categoryContainer = section.querySelector(`#category-${categoryId}`);
            this.renderLinksInContainer(categoryLinks, categoryContainer);
        });

        // Re-initialize Lucide icons
        lucide.createIcons();
    }

    renderLinksInContainer(links, container) {
        links.forEach((link, index) => {
            const card = this.createLinkCard(link, index);
            container.appendChild(card);
        });
    }

    createLinkCard(link, index) {
        const card = document.createElement('div');
        card.className = `link-card glass-card p-6 ${this.selectedLinks.has(link.id) ? 'selected' : ''}`;
        card.dataset.id = link.id;

        const preview = link.preview || {};
        const domain = preview.domain || new URL(link.url).hostname;
        const isSelected = this.selectedLinks.has(link.id);

        card.innerHTML = `
            <div class="selection-checkbox">
                <input type="checkbox" ${isSelected ? 'checked' : ''}
                       onchange="linkManager.toggleLinkSelection('${link.id}')"
                       class="rounded border-gray-300">
            </div>

            <div class="card-actions">
                <button onclick="linkManager.editLink('${link.id}')" class="glass-button p-1" title="Edit">
                    <i data-lucide="edit-2" class="w-4 h-4"></i>
                </button>
                <button onclick="linkManager.deleteLink('${link.id}')" class="glass-button p-1 text-red-600" title="Delete">
                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                </button>
            </div>

            ${preview.image ? `<img src="${link.customImage || preview.image}" alt="Preview" class="link-preview-image" onerror="this.style.display='none'">` : ''}

            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center min-w-0 flex-1">
                    ${this.settings.showFavicons ? `<img src="${this.getFaviconUrl(link.url)}" alt="Favicon" class="link-favicon" onerror="this.style.display='none'">` : ''}
                    <span class="text-xs text-gray-500 dark:text-gray-400 truncate">${domain}</span>
                </div>
                <div class="flex space-x-1 ml-2">
                    ${link.categories ? link.categories.map(catId => {
                        const cat = this.categories.find(c => c.id === catId);
                        return cat ? `<div class="w-2 h-2 rounded-full" style="background-color: ${cat.color}" title="${cat.name}"></div>` : '';
                    }).join('') : ''}
                </div>
            </div>

            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2 line-clamp-2">
                ${link.title}
            </h3>

            ${link.description ? `<div class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-3">${this.getDescriptionText(link.description)}</div>` : ''}

            ${link.tags && link.tags.length > 0 ? `
                <div class="flex flex-wrap gap-1 mb-3">
                    ${link.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
            ` : ''}

            <div class="flex items-center justify-between">
                <a href="${link.url}" target="${this.settings.newTab ? '_blank' : '_self'}" rel="noopener noreferrer"
                   onclick="linkManager.trackVisit('${link.id}')"
                   class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors">
                    Visit Link
                    <i data-lucide="external-link" class="w-4 h-4 ml-1"></i>
                </a>
                <div class="text-xs text-gray-400">
                    ${link.visitCount > 0 ? `${link.visitCount} visits` : 'Never visited'}
                </div>
            </div>
        `;

        return card;
    }

    getFilteredLinks() {
        let filtered = this.links;

        // Filter by category
        if (this.currentCategory) {
            filtered = filtered.filter(link =>
                link.categories && link.categories.includes(this.currentCategory)
            );
        }

        // Filter by search term
        if (this.searchTerm) {
            filtered = filtered.filter(link =>
                link.title.toLowerCase().includes(this.searchTerm) ||
                link.url.toLowerCase().includes(this.searchTerm) ||
                (link.description && this.getDescriptionText(link.description).toLowerCase().includes(this.searchTerm)) ||
                (link.tags && link.tags.some(tag => tag.toLowerCase().includes(this.searchTerm)))
            );
        }

        // Apply other filters
        if (this.filters.tag) {
            filtered = filtered.filter(link =>
                link.tags && link.tags.includes(this.filters.tag)
            );
        }

        if (this.filters.domain) {
            filtered = filtered.filter(link => {
                const domain = link.preview?.domain || new URL(link.url).hostname;
                return domain === this.filters.domain;
            });
        }

        // Date range filter
        if (this.filters.dateFrom) {
            const fromDate = new Date(this.filters.dateFrom);
            filtered = filtered.filter(link => new Date(link.dateAdded) >= fromDate);
        }

        if (this.filters.dateTo) {
            const toDate = new Date(this.filters.dateTo);
            toDate.setHours(23, 59, 59, 999);
            filtered = filtered.filter(link => new Date(link.dateAdded) <= toDate);
        }

        // Type filter
        if (this.filters.type) {
            filtered = filtered.filter(link => {
                const domain = link.preview?.domain || new URL(link.url).hostname;
                switch (this.filters.type) {
                    case 'youtube':
                        return domain.includes('youtube.com') || domain.includes('youtu.be');
                    case 'github':
                        return domain.includes('github.com');
                    case 'twitter':
                        return domain.includes('twitter.com') || domain.includes('x.com');
                    case 'article':
                        return !domain.includes('youtube.com') && !domain.includes('github.com') && !domain.includes('twitter.com');
                    default:
                        return true;
                }
            });
        }

        // Preview filter
        if (this.filters.hasPreview) {
            const hasPreview = this.filters.hasPreview === 'true';
            filtered = filtered.filter(link =>
                hasPreview ? (link.preview && link.preview.image) : (!link.preview || !link.preview.image)
            );
        }

        // Sort
        filtered.sort((a, b) => {
            switch (this.filters.sort) {
                case 'title':
                    return a.title.localeCompare(b.title);
                case 'domain':
                    const domainA = a.preview?.domain || new URL(a.url).hostname;
                    const domainB = b.preview?.domain || new URL(b.url).hostname;
                    return domainA.localeCompare(domainB);
                case 'lastVisited':
                    return new Date(b.lastVisited || 0) - new Date(a.lastVisited || 0);
                case 'dateAdded':
                default:
                    return new Date(b.dateAdded) - new Date(a.dateAdded);
            }
        });

        return filtered;
    }

    // Utility Methods
    groupLinksByCategory(links) {
        const grouped = {};

        links.forEach(link => {
            if (link.categories && link.categories.length > 0) {
                link.categories.forEach(categoryId => {
                    if (!grouped[categoryId]) grouped[categoryId] = [];
                    grouped[categoryId].push(link);
                });
            } else {
                if (!grouped['uncategorized']) grouped['uncategorized'] = [];
                grouped['uncategorized'].push(link);
            }
        });

        return grouped;
    }

    getDescriptionText(description) {
        if (typeof description === 'string') return description;
        if (description && description.ops) {
            return description.ops.map(op => op.insert).join('').trim();
        }
        return '';
    }

    getFaviconUrl(url) {
        try {
            const domain = new URL(url).origin;
            return `${domain}/favicon.ico`;
        } catch {
            return 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M13.5 2c-5.621 0-10.211 4.443-10.475 10h-3.025l4 5.917 4-5.917h-2.938c.244-4.058 3.583-7.312 7.438-7.312 4.106 0 7.438 3.332 7.438 7.438s-3.332 7.438-7.438 7.438c-1.287 0-2.513-.328-3.563-.906l-1.706 2.938c1.538.894 3.31 1.406 5.269 1.406 5.621 0 10.211-4.443 10.475-10h3.025l-4-5.917-4 5.917h2.938c-.244 4.058-3.583 7.312-7.438 7.312z"/></svg>';
        }
    }

    toggleLinkSelection(linkId) {
        if (this.selectedLinks.has(linkId)) {
            this.selectedLinks.delete(linkId);
        } else {
            this.selectedLinks.add(linkId);
        }
        this.updateBulkActions();
        this.renderLinks();
    }

    trackVisit(linkId) {
        const link = this.links.find(l => l.id === linkId);
        if (link) {
            link.visitCount = (link.visitCount || 0) + 1;
            link.lastVisited = new Date().toISOString();
            this.saveLinks();
        }
    }

    toggleCategorySection(categoryId) {
        const section = document.querySelector(`[data-category="${categoryId}"]`);
        if (section) {
            section.classList.toggle('collapsed');
        }
    }

    populateCategoryOptions() {
        const select = document.getElementById('categories');
        if (!select) {
            console.warn('Categories select element not found');
            return;
        }

        select.innerHTML = '';

        this.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            select.appendChild(option);
        });
    }

    updateFilters() {
        this.updateTagFilter();
        this.updateDomainFilter();
    }

    updateTagFilter() {
        const tagFilter = document.getElementById('tagFilter');
        const allTags = [...new Set(this.links.flatMap(link => link.tags || []))].sort();

        tagFilter.innerHTML = '<option value="">All Tags</option>';
        allTags.forEach(tag => {
            const option = document.createElement('option');
            option.value = tag;
            option.textContent = tag;
            if (tag === this.filters.tag) option.selected = true;
            tagFilter.appendChild(option);
        });
    }

    updateDomainFilter() {
        const domainFilter = document.getElementById('domainFilter');
        const allDomains = [...new Set(this.links.map(link => {
            return link.preview?.domain || new URL(link.url).hostname;
        }))].sort();

        domainFilter.innerHTML = '<option value="">All Domains</option>';
        allDomains.forEach(domain => {
            const option = document.createElement('option');
            option.value = domain;
            option.textContent = domain;
            if (domain === this.filters.domain) option.selected = true;
            domainFilter.appendChild(option);
        });
    }

    setupSortable() {
        const containers = document.querySelectorAll('.grid');
        containers.forEach(container => {
            if (this.sortableInstance) {
                this.sortableInstance.destroy();
            }

            this.sortableInstance = Sortable.create(container, {
                animation: 150,
                ghostClass: 'opacity-50',
                onEnd: (evt) => {
                    // Handle reordering logic
                    this.handleReorder(evt);
                }
            });
        });
    }

    handleReorder(evt) {
        // Implementation for drag and drop reordering
        const filteredLinks = this.getFilteredLinks();
        const movedLink = filteredLinks[evt.oldIndex];
        const newIndex = evt.newIndex;

        if (movedLink) {
            this.saveState();
            // Update order in the main links array
            const originalIndex = this.links.findIndex(link => link.id === movedLink.id);
            this.links.splice(originalIndex, 1);
            this.links.splice(newIndex, 0, movedLink);
            this.saveLinks();
        }
    }

    // Settings and Theme Management
    populateSettingsForm() {
        document.getElementById('themeSelect').value = this.settings.theme;
        document.getElementById('cardSizeSelect').value = this.settings.cardSize;
        document.getElementById('autoPreviewSetting').checked = this.settings.autoPreview;
        document.getElementById('newTabSetting').checked = this.settings.newTab;
        document.getElementById('faviconSetting').checked = this.settings.showFavicons;
        document.getElementById('animationSetting').checked = this.settings.animations;
        document.getElementById('backupReminderSetting').checked = this.settings.backupReminder;
        document.getElementById('defaultViewSetting').value = this.settings.defaultView;
        document.getElementById('storageMethodSetting').value = this.settings.storageMethod;

        // Update storage info
        this.updateStorageInfo();
    }

    selectTheme(theme) {
        this.settings.colorScheme = theme;
        document.documentElement.setAttribute('data-theme', theme);

        // Update active theme button
        document.querySelectorAll('.theme-option').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.theme === theme);
        });
    }

    updateStorageInfo() {
        const used = this.calculateStorageUsed();
        document.getElementById('storageUsed').textContent = this.formatBytes(used);
        document.getElementById('totalLinks').textContent = this.links.length;
        document.getElementById('totalCategories').textContent = this.categories.length;
    }

    calculateStorageUsed() {
        const data = localStorage.getItem('links') + localStorage.getItem('categories') + localStorage.getItem('settings');
        return new Blob([data]).size;
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    checkBackupReminder() {
        if (!this.settings.backupReminder) return;

        const lastBackup = localStorage.getItem('lastBackupReminder');
        const now = Date.now();
        const dayInMs = 24 * 60 * 60 * 1000;

        if (!lastBackup || (now - parseInt(lastBackup)) > dayInMs) {
            setTimeout(() => {
                this.showToast('💾 Daily backup reminder: Consider exporting your links!', 'info', 5000);
                localStorage.setItem('lastBackupReminder', now.toString());
            }, 5000);
        }
    }

    // Export functionality placeholder
    exportData(format) {
        switch (format) {
            case 'json':
                this.exportJSON();
                break;
            case 'csv':
                this.exportCSV();
                break;
            case 'html':
                this.exportHTML();
                break;
            case 'markdown':
                this.exportMarkdown();
                break;
            case 'pdf':
                this.exportPDF();
                break;
            case 'share':
                this.createShareLink();
                break;
        }
        this.closeExportModal();
    }

    exportJSON() {
        const data = {
            links: this.links,
            categories: this.categories,
            exportDate: new Date().toISOString(),
            version: '2.0'
        };

        this.downloadFile(
            JSON.stringify(data, null, 2),
            `link-manager-export-${new Date().toISOString().split('T')[0]}.json`,
            'application/json'
        );

        this.showToast('Links exported as JSON!', 'success');
    }

    exportCSV() {
        const headers = ['Title', 'URL', 'Description', 'Tags', 'Categories', 'Date Added', 'Visit Count'];
        const rows = this.links.map(link => [
            link.title,
            link.url,
            this.getDescriptionText(link.description),
            (link.tags || []).join('; '),
            (link.categories || []).map(id => this.categories.find(c => c.id === id)?.name || id).join('; '),
            new Date(link.dateAdded).toLocaleDateString(),
            link.visitCount || 0
        ]);

        const csv = [headers, ...rows].map(row =>
            row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
        ).join('\n');

        this.downloadFile(
            csv,
            `link-manager-export-${new Date().toISOString().split('T')[0]}.csv`,
            'text/csv'
        );

        this.showToast('Links exported as CSV!', 'success');
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        URL.revokeObjectURL(url);
    }

    // Remaining Critical Methods Implementation
    handleEditLink(e) {
        e.preventDefault();

        const index = parseInt(document.getElementById('editIndex').value);
        const url = document.getElementById('editUrl').value.trim();
        const title = document.getElementById('editTitle').value.trim();
        const desc = document.getElementById('editDesc').value.trim();
        const tags = document.getElementById('editTags').value.trim();

        if (!url || !title) {
            this.showToast('URL and title are required', 'error');
            return;
        }

        if (!this.isValidUrl(url)) {
            this.showToast('Please enter a valid URL', 'error');
            return;
        }

        this.saveState();
        this.links[index] = {
            ...this.links[index],
            url: url,
            title: title,
            description: desc,
            tags: tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : []
        };

        this.saveLinks();
        this.renderLinks();
        this.updateFilters();
        this.closeEditModal();
        this.showToast('Link updated successfully!', 'success');
    }

    editLink(linkId) {
        const link = this.links.find(l => l.id === linkId);
        if (!link) return;

        const index = this.links.findIndex(l => l.id === linkId);

        document.getElementById('editIndex').value = index;
        document.getElementById('editUrl').value = link.url;
        document.getElementById('editTitle').value = link.title;
        document.getElementById('editDesc').value = this.getDescriptionText(link.description) || '';
        document.getElementById('editTags').value = link.tags ? link.tags.join(', ') : '';

        this.openEditModal();
    }

    deleteLink(linkId) {
        const link = this.links.find(l => l.id === linkId);
        if (!link) return;

        if (confirm(`Delete "${link.title}"?`)) {
            this.saveState();
            this.links = this.links.filter(l => l.id !== linkId);
            this.saveLinks();
            this.renderLinks();
            this.updateStats();
            this.updateFilters();
            this.showToast('Link deleted successfully!', 'success');
        }
    }

    openEditModal() {
        document.getElementById('editModal').classList.remove('hidden');
    }

    closeEditModal() {
        document.getElementById('editModal').classList.add('hidden');
    }

    editCategory(categoryId) {
        const category = this.categories.find(c => c.id === categoryId);
        if (!category) return;

        const newName = prompt('Enter new category name:', category.name);
        if (newName && newName.trim() && newName.trim() !== category.name) {
            this.saveState();
            category.name = newName.trim();
            this.saveCategories();
            this.renderCategories();
            this.renderCategoryList();
            this.showToast(`Category renamed to "${newName}"`, 'success');
        }
    }

    showAllCategories() {
        this.currentCategory = '';
        this.updateCategorySelection();
        this.renderLinks();
    }

    handleFileImport(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const content = e.target.result;
                let importedData;

                if (file.name.endsWith('.json')) {
                    importedData = JSON.parse(content);
                    this.importJSON(importedData);
                } else if (file.name.endsWith('.html')) {
                    this.importBookmarks(content);
                } else {
                    throw new Error('Unsupported file format');
                }
            } catch (error) {
                console.error('Import failed:', error);
                this.showToast('Failed to import file. Please check the format.', 'error');
            }
        };
        reader.readAsText(file);

        // Reset file input
        e.target.value = '';
    }

    importJSON(data) {
        if (data.links && Array.isArray(data.links)) {
            this.saveState();

            // Merge links
            const existingUrls = new Set(this.links.map(link => link.url));
            const newLinks = data.links.filter(link => !existingUrls.has(link.url));

            this.links = [...this.links, ...newLinks];

            // Merge categories if present
            if (data.categories && Array.isArray(data.categories)) {
                const existingCategoryIds = new Set(this.categories.map(cat => cat.id));
                const newCategories = data.categories.filter(cat => !existingCategoryIds.has(cat.id));
                this.categories = [...this.categories, ...newCategories];
                this.saveCategories();
            }

            this.saveLinks();
            this.renderCategories();
            this.renderLinks();
            this.updateStats();
            this.updateFilters();

            this.showToast(`Imported ${newLinks.length} new links successfully!`, 'success');
        } else {
            throw new Error('Invalid JSON format');
        }
    }

    importBookmarks(htmlContent) {
        // Simple bookmark parser
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const links = doc.querySelectorAll('a[href]');

        if (links.length === 0) {
            throw new Error('No bookmarks found in HTML file');
        }

        this.saveState();
        const existingUrls = new Set(this.links.map(link => link.url));
        let importedCount = 0;

        links.forEach(linkElement => {
            const url = linkElement.href;
            const title = linkElement.textContent.trim() || url;

            if (!existingUrls.has(url)) {
                const newLink = {
                    id: this.generateId(),
                    url: url,
                    title: title,
                    description: '',
                    tags: [],
                    categories: [],
                    preview: null,
                    dateAdded: new Date().toISOString(),
                    lastVisited: null,
                    visitCount: 0,
                    order: this.links.length + importedCount
                };

                this.links.push(newLink);
                existingUrls.add(url);
                importedCount++;
            }
        });

        this.saveLinks();
        this.renderLinks();
        this.updateStats();
        this.updateFilters();

        this.showToast(`Imported ${importedCount} bookmarks successfully!`, 'success');
    }

    openImportModal() {
        document.getElementById('fileInput').click();
        document.getElementById('dropdownMenu').classList.add('hidden');
    }

    showBulkMoveModal() {
        // Simple implementation - could be enhanced with a proper modal
        const categoryNames = this.categories.map(cat => cat.name);
        const selectedCategory = prompt(`Move ${this.selectedLinks.size} links to category:\n\n${categoryNames.join('\n')}\n\nEnter category name:`);

        if (selectedCategory) {
            const category = this.categories.find(cat => cat.name.toLowerCase() === selectedCategory.toLowerCase());
            if (category) {
                this.saveState();
                this.links.forEach(link => {
                    if (this.selectedLinks.has(link.id)) {
                        if (!link.categories) link.categories = [];
                        if (!link.categories.includes(category.id)) {
                            link.categories.push(category.id);
                        }
                    }
                });

                this.saveLinks();
                this.renderLinks();
                this.clearSelection();
                this.showToast(`Moved ${this.selectedLinks.size} links to "${category.name}"`, 'success');
            } else {
                this.showToast('Category not found', 'error');
            }
        }
    }

    exportSelectedLinks(links) {
        const data = {
            links: links,
            categories: this.categories,
            exportDate: new Date().toISOString(),
            version: '3.0'
        };

        this.downloadFile(
            JSON.stringify(data, null, 2),
            `selected-links-export-${new Date().toISOString().split('T')[0]}.json`,
            'application/json'
        );

        this.showToast(`Exported ${links.length} selected links!`, 'success');
    }

    renderStatistics() {
        const container = document.getElementById('statsContent');
        const totalLinks = this.links.length;
        const totalCategories = this.categories.length;
        const totalTags = [...new Set(this.links.flatMap(link => link.tags || []))].length;

        // Calculate visit statistics
        const totalVisits = this.links.reduce((sum, link) => sum + (link.visitCount || 0), 0);
        const mostVisited = this.links
            .filter(link => link.visitCount > 0)
            .sort((a, b) => (b.visitCount || 0) - (a.visitCount || 0))
            .slice(0, 5);

        // Calculate recent additions
        const recentLinks = this.links
            .sort((a, b) => new Date(b.dateAdded) - new Date(a.dateAdded))
            .slice(0, 5);

        // Domain statistics
        const domainCounts = {};
        this.links.forEach(link => {
            const domain = link.preview?.domain || new URL(link.url).hostname;
            domainCounts[domain] = (domainCounts[domain] || 0) + 1;
        });

        const topDomains = Object.entries(domainCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);

        container.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Overview Stats -->
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2"></i>
                        Overview
                    </h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span>Total Links</span>
                            <span class="font-semibold">${totalLinks}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Categories</span>
                            <span class="font-semibold">${totalCategories}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Unique Tags</span>
                            <span class="font-semibold">${totalTags}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Total Visits</span>
                            <span class="font-semibold">${totalVisits}</span>
                        </div>
                    </div>
                </div>

                <!-- Most Visited -->
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i data-lucide="trending-up" class="w-5 h-5 mr-2"></i>
                        Most Visited
                    </h3>
                    <div class="space-y-2">
                        ${mostVisited.length > 0 ? mostVisited.map(link => `
                            <div class="flex justify-between items-center">
                                <span class="truncate flex-1 mr-2">${link.title}</span>
                                <span class="text-sm text-gray-500">${link.visitCount}</span>
                            </div>
                        `).join('') : '<p class="text-gray-500">No visits yet</p>'}
                    </div>
                </div>

                <!-- Top Domains -->
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i data-lucide="globe" class="w-5 h-5 mr-2"></i>
                        Top Domains
                    </h3>
                    <div class="space-y-2">
                        ${topDomains.map(([domain, count]) => `
                            <div class="flex justify-between items-center">
                                <span class="truncate flex-1 mr-2">${domain}</span>
                                <span class="text-sm text-gray-500">${count}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- Recent Additions -->
                <div class="glass-card p-6 md:col-span-2">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i data-lucide="clock" class="w-5 h-5 mr-2"></i>
                        Recent Additions
                    </h3>
                    <div class="space-y-2">
                        ${recentLinks.map(link => `
                            <div class="flex justify-between items-center">
                                <span class="truncate flex-1 mr-2">${link.title}</span>
                                <span class="text-sm text-gray-500">${new Date(link.dateAdded).toLocaleDateString()}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- Storage Info -->
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i data-lucide="hard-drive" class="w-5 h-5 mr-2"></i>
                        Storage
                    </h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span>Used Space</span>
                            <span class="font-semibold">${this.formatBytes(this.calculateStorageUsed())}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Cache Size</span>
                            <span class="font-semibold">${this.formatBytes(this.calculateCacheSize())}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Re-initialize Lucide icons
        lucide.createIcons();
    }

    calculateCacheSize() {
        const cacheData = localStorage.getItem('linkPreviews') || '{}';
        return new Blob([cacheData]).size;
    }

    exportHTML() {
        const html = this.generateHTMLExport();
        this.downloadFile(
            html,
            `link-manager-export-${new Date().toISOString().split('T')[0]}.html`,
            'text/html'
        );
        this.showToast('Links exported as HTML!', 'success');
    }

    generateHTMLExport() {
        const linksByCategory = this.groupLinksByCategory(this.links);

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Link Collection</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 40px; }
        .category { margin-bottom: 30px; background: white; border-radius: 12px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .category-title { font-size: 24px; font-weight: bold; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 2px solid #eee; }
        .link-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; }
        .link-card { border: 1px solid #eee; border-radius: 8px; padding: 15px; background: #fafafa; }
        .link-title { font-weight: bold; margin-bottom: 5px; }
        .link-url { color: #666; font-size: 14px; word-break: break-all; }
        .link-desc { margin-top: 8px; color: #555; font-size: 14px; }
        .tags { margin-top: 8px; }
        .tag { display: inline-block; background: #e3f2fd; color: #1976d2; padding: 2px 8px; border-radius: 4px; font-size: 12px; margin-right: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>My Link Collection</h1>
            <p>Exported on ${new Date().toLocaleDateString()}</p>
        </div>

        ${Object.entries(linksByCategory).map(([categoryId, links]) => {
            const category = this.categories.find(c => c.id === categoryId) || { name: 'Uncategorized', color: '#666' };
            return `
                <div class="category">
                    <div class="category-title" style="color: ${category.color}">
                        ${category.name} (${links.length} links)
                    </div>
                    <div class="link-grid">
                        ${links.map(link => `
                            <div class="link-card">
                                <div class="link-title">${link.title}</div>
                                <div class="link-url">
                                    <a href="${link.url}" target="_blank">${link.url}</a>
                                </div>
                                ${link.description ? `<div class="link-desc">${this.getDescriptionText(link.description)}</div>` : ''}
                                ${link.tags && link.tags.length > 0 ? `
                                    <div class="tags">
                                        ${link.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                                    </div>
                                ` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }).join('')}
    </div>
</body>
</html>`;
    }

    exportMarkdown() {
        const markdown = this.generateMarkdownExport();
        this.downloadFile(
            markdown,
            `link-manager-export-${new Date().toISOString().split('T')[0]}.md`,
            'text/markdown'
        );
        this.showToast('Links exported as Markdown!', 'success');
    }

    generateMarkdownExport() {
        const linksByCategory = this.groupLinksByCategory(this.links);

        let markdown = `# My Link Collection\n\nExported on ${new Date().toLocaleDateString()}\n\n`;

        Object.entries(linksByCategory).forEach(([categoryId, links]) => {
            const category = this.categories.find(c => c.id === categoryId) || { name: 'Uncategorized' };
            markdown += `## ${category.name} (${links.length} links)\n\n`;

            links.forEach(link => {
                markdown += `### [${link.title}](${link.url})\n\n`;
                if (link.description) {
                    markdown += `${this.getDescriptionText(link.description)}\n\n`;
                }
                if (link.tags && link.tags.length > 0) {
                    markdown += `**Tags:** ${link.tags.map(tag => `\`${tag}\``).join(', ')}\n\n`;
                }
                markdown += `**URL:** ${link.url}\n\n`;
                markdown += '---\n\n';
            });
        });

        return markdown;
    }

    exportPDF() {
        // Simple PDF export using jsPDF
        if (typeof jsPDF === 'undefined') {
            this.showToast('PDF export not available. jsPDF library not loaded.', 'error');
            return;
        }

        this.showToast('Generating PDF... This may take a moment.', 'info');

        setTimeout(() => {
            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                // Add title
                doc.setFontSize(20);
                doc.text('My Link Collection', 20, 30);

                doc.setFontSize(12);
                doc.text(`Exported on ${new Date().toLocaleDateString()}`, 20, 45);

                let yPosition = 60;
                const pageHeight = doc.internal.pageSize.height;

                const linksByCategory = this.groupLinksByCategory(this.links);

                Object.entries(linksByCategory).forEach(([categoryId, links]) => {
                    const category = this.categories.find(c => c.id === categoryId) || { name: 'Uncategorized' };

                    // Check if we need a new page
                    if (yPosition > pageHeight - 60) {
                        doc.addPage();
                        yPosition = 30;
                    }

                    // Category title
                    doc.setFontSize(16);
                    doc.text(`${category.name} (${links.length} links)`, 20, yPosition);
                    yPosition += 15;

                    doc.setFontSize(10);

                    links.forEach(link => {
                        if (yPosition > pageHeight - 40) {
                            doc.addPage();
                            yPosition = 30;
                        }

                        // Link title
                        doc.setFont(undefined, 'bold');
                        doc.text(link.title, 25, yPosition);
                        yPosition += 8;

                        // URL
                        doc.setFont(undefined, 'normal');
                        doc.text(link.url, 25, yPosition);
                        yPosition += 8;

                        // Description (if exists)
                        if (link.description) {
                            const desc = this.getDescriptionText(link.description);
                            if (desc) {
                                const lines = doc.splitTextToSize(desc, 160);
                                doc.text(lines, 25, yPosition);
                                yPosition += lines.length * 5;
                            }
                        }

                        yPosition += 5;
                    });

                    yPosition += 10;
                });

                doc.save(`link-manager-export-${new Date().toISOString().split('T')[0]}.pdf`);
                this.showToast('PDF exported successfully!', 'success');
            } catch (error) {
                console.error('PDF export failed:', error);
                this.showToast('PDF export failed. Please try again.', 'error');
            }
        }, 100);
    }

    createShareLink() {
        // Simple share functionality - could be enhanced with cloud storage
        const shareData = {
            links: this.links,
            categories: this.categories,
            title: 'My Link Collection',
            created: new Date().toISOString()
        };

        const dataStr = JSON.stringify(shareData);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        // Copy to clipboard if possible
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                this.showToast('Share link copied to clipboard!', 'success');
            }).catch(() => {
                this.showToast('Share link created. Check downloads folder.', 'info');
                this.downloadFile(dataStr, 'shared-links.json', 'application/json');
            });
        } else {
            this.showToast('Share link created. Check downloads folder.', 'info');
            this.downloadFile(dataStr, 'shared-links.json', 'application/json');
        }

        URL.revokeObjectURL(url);
    }
}

// Global functions for onclick handlers
function openAddModal() {
    linkManager.openAddModal();
}

function closeAddModal() {
    linkManager.closeAddModal();
}

function closeEditModal() {
    linkManager.closeEditModal();
}

function closeCategoryModal() {
    linkManager.closeCategoryModal();
}

function closeSettingsModal() {
    linkManager.closeSettingsModal();
}

function closeExportModal() {
    linkManager.closeExportModal();
}

function closeStatsModal() {
    linkManager.closeStatsModal();
}

// Additional missing methods for the EnhancedLinkManager class
EnhancedLinkManager.prototype.populateSettingsForm = function() {
    // Populate settings form with current values
    const themeSelect = document.getElementById('themeSelect');
    const cardSizeSelect = document.getElementById('cardSizeSelect');
    const autoPreviewSetting = document.getElementById('autoPreviewSetting');
    const newTabSetting = document.getElementById('newTabSetting');
    const faviconSetting = document.getElementById('faviconSetting');
    const animationSetting = document.getElementById('animationSetting');
    const backupReminderSetting = document.getElementById('backupReminderSetting');
    const defaultViewSetting = document.getElementById('defaultViewSetting');
    const storageMethodSetting = document.getElementById('storageMethodSetting');

    if (themeSelect) themeSelect.value = this.settings.theme;
    if (cardSizeSelect) cardSizeSelect.value = this.settings.cardSize;
    if (autoPreviewSetting) autoPreviewSetting.checked = this.settings.autoPreview;
    if (newTabSetting) newTabSetting.checked = this.settings.newTab;
    if (faviconSetting) faviconSetting.checked = this.settings.showFavicons;
    if (animationSetting) animationSetting.checked = this.settings.animations;
    if (backupReminderSetting) backupReminderSetting.checked = this.settings.backupReminder;
    if (defaultViewSetting) defaultViewSetting.value = this.settings.defaultView;
    if (storageMethodSetting) storageMethodSetting.value = this.settings.storageMethod;

    this.updateStorageInfo();
};

EnhancedLinkManager.prototype.saveSettings = function() {
    // Collect settings from form
    const themeSelect = document.getElementById('themeSelect');
    const cardSizeSelect = document.getElementById('cardSizeSelect');
    const autoPreviewSetting = document.getElementById('autoPreviewSetting');
    const newTabSetting = document.getElementById('newTabSetting');
    const faviconSetting = document.getElementById('faviconSetting');
    const animationSetting = document.getElementById('animationSetting');
    const backupReminderSetting = document.getElementById('backupReminderSetting');
    const defaultViewSetting = document.getElementById('defaultViewSetting');
    const storageMethodSetting = document.getElementById('storageMethodSetting');

    if (themeSelect) this.settings.theme = themeSelect.value;
    if (cardSizeSelect) this.settings.cardSize = cardSizeSelect.value;
    if (autoPreviewSetting) this.settings.autoPreview = autoPreviewSetting.checked;
    if (newTabSetting) this.settings.newTab = newTabSetting.checked;
    if (faviconSetting) this.settings.showFavicons = faviconSetting.checked;
    if (animationSetting) this.settings.animations = animationSetting.checked;
    if (backupReminderSetting) this.settings.backupReminder = backupReminderSetting.checked;
    if (defaultViewSetting) this.settings.defaultView = defaultViewSetting.value;
    if (storageMethodSetting) this.settings.storageMethod = storageMethodSetting.value;

    // Apply settings
    this.applySettings();
    localStorage.setItem('settings', JSON.stringify(this.settings));

    this.closeSettingsModal();
    this.showToast('Settings saved successfully!', 'success');
};

// Initialize the enhanced app
const linkManager = new EnhancedLinkManager();
