// Enhanced Link Manager with Glassmorphism UI
class LinkManager {
    constructor() {
        this.links = JSON.parse(localStorage.getItem('links')) || [];
        this.currentView = localStorage.getItem('viewMode') || 'grid';
        this.isDarkMode = localStorage.getItem('darkMode') === 'true';
        this.sortableInstance = null;
        this.searchTerm = '';
        this.selectedTag = '';
        this.selectedDomain = '';
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupDarkMode();
        this.setupViewMode();
        this.renderLinks();
        this.updateFilters();
        this.setupSortable();
    }

    setupEventListeners() {
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.searchTerm = e.target.value.toLowerCase();
            this.renderLinks();
        });

        // Filter functionality
        document.getElementById('tagFilter').addEventListener('change', (e) => {
            this.selectedTag = e.target.value;
            this.renderLinks();
        });

        document.getElementById('domainFilter').addEventListener('change', (e) => {
            this.selectedDomain = e.target.value;
            this.renderLinks();
        });

        // View mode toggle
        document.getElementById('gridViewBtn').addEventListener('click', () => this.setViewMode('grid'));
        document.getElementById('listViewBtn').addEventListener('click', () => this.setViewMode('list'));

        // Dark mode toggle
        document.getElementById('darkModeToggle').addEventListener('click', () => this.toggleDarkMode());

        // Menu toggle
        document.getElementById('menuToggle').addEventListener('click', () => this.toggleMenu());

        // Export/Import
        document.getElementById('exportBtn').addEventListener('click', () => this.exportLinks());
        document.getElementById('importBtn').addEventListener('click', () => this.importLinks());
        document.getElementById('fileInput').addEventListener('change', (e) => this.handleFileImport(e));

        // Form submissions
        document.getElementById('addLinkForm').addEventListener('submit', (e) => this.handleAddLink(e));
        document.getElementById('editLinkForm').addEventListener('submit', (e) => this.handleEditLink(e));

        // URL preview
        document.getElementById('url').addEventListener('input', debounce((e) => this.handleUrlPreview(e), 500));

        // Close modals on escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAddModal();
                this.closeEditModal();
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('#menuToggle') && !e.target.closest('#dropdownMenu')) {
                document.getElementById('dropdownMenu').classList.add('hidden');
            }
        });
    }

    setupDarkMode() {
        if (this.isDarkMode) {
            document.documentElement.classList.add('dark');
        }
    }

    toggleDarkMode() {
        this.isDarkMode = !this.isDarkMode;
        document.documentElement.classList.toggle('dark');
        localStorage.setItem('darkMode', this.isDarkMode);
    }

    setupViewMode() {
        this.setViewMode(this.currentView);
    }

    setViewMode(mode) {
        this.currentView = mode;
        localStorage.setItem('viewMode', mode);
        
        const container = document.getElementById('linkContainer');
        const gridBtn = document.getElementById('gridViewBtn');
        const listBtn = document.getElementById('listViewBtn');
        
        if (mode === 'grid') {
            container.className = 'grid-view grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
            gridBtn.classList.add('active');
            listBtn.classList.remove('active');
        } else {
            container.className = 'list-view flex flex-col gap-4';
            listBtn.classList.add('active');
            gridBtn.classList.remove('active');
        }
    }

    toggleMenu() {
        const menu = document.getElementById('dropdownMenu');
        menu.classList.toggle('hidden');
    }

    async handleUrlPreview(e) {
        const url = e.target.value.trim();
        const previewDiv = document.getElementById('urlPreview');
        
        if (!url || !this.isValidUrl(url)) {
            previewDiv.classList.add('hidden');
            return;
        }

        try {
            const preview = await window.linkPreviewService.getPreview(url);
            this.displayUrlPreview(preview);
        } catch (error) {
            console.warn('Preview failed:', error);
            previewDiv.classList.add('hidden');
        }
    }

    displayUrlPreview(preview) {
        const previewDiv = document.getElementById('urlPreview');
        const titleInput = document.getElementById('title');
        const descInput = document.getElementById('desc');
        
        document.getElementById('previewImage').src = preview.image;
        document.getElementById('previewTitle').textContent = preview.title;
        document.getElementById('previewDescription').textContent = preview.description;
        document.getElementById('previewDomain').textContent = preview.domain;
        
        // Auto-fill form if empty
        if (!titleInput.value) titleInput.value = preview.title;
        if (!descInput.value) descInput.value = preview.description;
        
        previewDiv.classList.remove('hidden');
    }

    async handleAddLink(e) {
        e.preventDefault();
        
        const url = document.getElementById('url').value.trim();
        const title = document.getElementById('title').value.trim();
        const desc = document.getElementById('desc').value.trim();
        const tags = document.getElementById('tags').value.trim();
        
        if (!url) {
            alert('URL is required');
            return;
        }

        if (!this.isValidUrl(url)) {
            alert('Please enter a valid URL');
            return;
        }

        this.setLoadingState(true);

        try {
            const preview = await window.linkPreviewService.getPreview(url);
            
            const newLink = {
                id: Date.now(),
                url: url,
                title: title || preview.title,
                description: desc || preview.description,
                tags: tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
                preview: preview,
                dateAdded: new Date().toISOString(),
                order: this.links.length
            };

            this.links.push(newLink);
            this.saveLinks();
            this.renderLinks();
            this.updateFilters();
            this.closeAddModal();
            this.resetAddForm();
            
            // Show success animation
            this.showNotification('Link added successfully!', 'success');
        } catch (error) {
            console.error('Failed to add link:', error);
            this.showNotification('Failed to add link. Please try again.', 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    handleEditLink(e) {
        e.preventDefault();
        
        const index = parseInt(document.getElementById('editIndex').value);
        const url = document.getElementById('editUrl').value.trim();
        const title = document.getElementById('editTitle').value.trim();
        const desc = document.getElementById('editDesc').value.trim();
        const tags = document.getElementById('editTags').value.trim();
        
        if (!url || !title) {
            alert('URL and title are required');
            return;
        }

        this.links[index] = {
            ...this.links[index],
            url: url,
            title: title,
            description: desc,
            tags: tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : []
        };

        this.saveLinks();
        this.renderLinks();
        this.updateFilters();
        this.closeEditModal();
        this.showNotification('Link updated successfully!', 'success');
    }

    deleteLink(index) {
        if (confirm('Are you sure you want to delete this link?')) {
            this.links.splice(index, 1);
            this.saveLinks();
            this.renderLinks();
            this.updateFilters();
            this.showNotification('Link deleted successfully!', 'success');
        }
    }

    editLink(index) {
        const link = this.links[index];
        
        document.getElementById('editIndex').value = index;
        document.getElementById('editUrl').value = link.url;
        document.getElementById('editTitle').value = link.title;
        document.getElementById('editDesc').value = link.description || '';
        document.getElementById('editTags').value = link.tags ? link.tags.join(', ') : '';
        
        this.openEditModal();
    }

    renderLinks() {
        const container = document.getElementById('linkContainer');
        const emptyState = document.getElementById('emptyState');
        
        const filteredLinks = this.getFilteredLinks();
        
        if (filteredLinks.length === 0) {
            container.innerHTML = '';
            emptyState.classList.remove('hidden');
            return;
        }
        
        emptyState.classList.add('hidden');
        container.innerHTML = '';
        
        filteredLinks.forEach((link, index) => {
            const card = this.createLinkCard(link, index);
            container.appendChild(card);
        });
        
        this.setupSortable();
    }

    createLinkCard(link, index) {
        const card = document.createElement('div');
        card.className = 'link-card glass-card p-6';
        card.dataset.id = link.id;
        
        const preview = link.preview || {};
        const domain = preview.domain || new URL(link.url).hostname;
        
        card.innerHTML = `
            ${preview.image ? `<img src="${preview.image}" alt="Preview" class="link-preview-image" onerror="this.style.display='none'">` : ''}
            
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center min-w-0 flex-1">
                    <img src="${preview.image || this.getFaviconUrl(link.url)}" alt="Favicon" class="link-favicon" onerror="this.src='data:image/svg+xml,<svg xmlns=\\"http://www.w3.org/2000/svg\\" viewBox=\\"0 0 24 24\\" fill=\\"currentColor\\"><path d=\\"M13.5 2c-5.621 0-10.211 4.443-10.475 10h-3.025l4 5.917 4-5.917h-2.938c.244-4.058 3.583-7.312 7.438-7.312 4.106 0 7.438 3.332 7.438 7.438s-3.332 7.438-7.438 7.438c-1.287 0-2.513-.328-3.563-.906l-1.706 2.938c1.538.894 3.31 1.406 5.269 1.406 5.621 0 10.211-4.443 10.475-10h3.025l-4-5.917-4 5.917h2.938c-.244 4.058-3.583 7.312-7.438 7.312z\\"/></svg>'">
                    <span class="text-xs text-gray-500 dark:text-gray-400 truncate">${domain}</span>
                </div>
                <div class="flex space-x-2 ml-2">
                    <button onclick="linkManager.editLink(${index})" class="text-blue-500 hover:text-blue-700 transition-colors" title="Edit">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                        </svg>
                    </button>
                    <button onclick="linkManager.deleteLink(${index})" class="text-red-500 hover:text-red-700 transition-colors" title="Delete">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2 line-clamp-2">
                ${link.title}
            </h3>
            
            ${link.description ? `<p class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-3">${link.description}</p>` : ''}
            
            ${link.tags && link.tags.length > 0 ? `
                <div class="flex flex-wrap gap-1 mb-3">
                    ${link.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
            ` : ''}
            
            <a href="${link.url}" target="_blank" rel="noopener noreferrer" 
               class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors">
                Visit Link
                <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
            </a>
        `;
        
        return card;
    }

    getFilteredLinks() {
        return this.links.filter(link => {
            const matchesSearch = !this.searchTerm || 
                link.title.toLowerCase().includes(this.searchTerm) ||
                link.description?.toLowerCase().includes(this.searchTerm) ||
                link.url.toLowerCase().includes(this.searchTerm) ||
                link.tags?.some(tag => tag.toLowerCase().includes(this.searchTerm));
            
            const matchesTag = !this.selectedTag || 
                link.tags?.includes(this.selectedTag);
            
            const linkDomain = link.preview?.domain || new URL(link.url).hostname;
            const matchesDomain = !this.selectedDomain || 
                linkDomain === this.selectedDomain;
            
            return matchesSearch && matchesTag && matchesDomain;
        });
    }

    updateFilters() {
        this.updateTagFilter();
        this.updateDomainFilter();
    }

    updateTagFilter() {
        const tagFilter = document.getElementById('tagFilter');
        const allTags = [...new Set(this.links.flatMap(link => link.tags || []))].sort();
        
        tagFilter.innerHTML = '<option value="">All Tags</option>';
        allTags.forEach(tag => {
            const option = document.createElement('option');
            option.value = tag;
            option.textContent = tag;
            if (tag === this.selectedTag) option.selected = true;
            tagFilter.appendChild(option);
        });
    }

    updateDomainFilter() {
        const domainFilter = document.getElementById('domainFilter');
        const allDomains = [...new Set(this.links.map(link => {
            return link.preview?.domain || new URL(link.url).hostname;
        }))].sort();
        
        domainFilter.innerHTML = '<option value="">All Domains</option>';
        allDomains.forEach(domain => {
            const option = document.createElement('option');
            option.value = domain;
            option.textContent = domain;
            if (domain === this.selectedDomain) option.selected = true;
            domainFilter.appendChild(option);
        });
    }

    setupSortable() {
        const container = document.getElementById('linkContainer');
        if (this.sortableInstance) {
            this.sortableInstance.destroy();
        }
        
        this.sortableInstance = Sortable.create(container, {
            animation: 150,
            ghostClass: 'opacity-50',
            onEnd: (evt) => {
                const filteredLinks = this.getFilteredLinks();
                const movedLink = filteredLinks[evt.oldIndex];
                const newIndex = evt.newIndex;
                
                // Update order in the main links array
                const originalIndex = this.links.findIndex(link => link.id === movedLink.id);
                this.links.splice(originalIndex, 1);
                this.links.splice(newIndex, 0, movedLink);
                
                this.saveLinks();
            }
        });
    }

    // Modal functions
    openAddModal() {
        document.getElementById('addModal').classList.remove('hidden');
        document.getElementById('url').focus();
    }

    closeAddModal() {
        document.getElementById('addModal').classList.add('hidden');
        this.resetAddForm();
    }

    openEditModal() {
        document.getElementById('editModal').classList.remove('hidden');
    }

    closeEditModal() {
        document.getElementById('editModal').classList.add('hidden');
    }

    resetAddForm() {
        document.getElementById('addLinkForm').reset();
        document.getElementById('urlPreview').classList.add('hidden');
    }

    // Utility functions
    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    getFaviconUrl(url) {
        try {
            const domain = new URL(url).origin;
            return `${domain}/favicon.ico`;
        } catch {
            return 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M13.5 2c-5.621 0-10.211 4.443-10.475 10h-3.025l4 5.917 4-5.917h-2.938c.244-4.058 3.583-7.312 7.438-7.312 4.106 0 7.438 3.332 7.438 7.438s-3.332 7.438-7.438 7.438c-1.287 0-2.513-.328-3.563-.906l-1.706 2.938c1.538.894 3.31 1.406 5.269 1.406 5.621 0 10.211-4.443 10.475-10h3.025l-4-5.917-4 5.917h2.938c-.244 4.058-3.583 7.312-7.438 7.312z"/></svg>';
        }
    }

    setLoadingState(loading) {
        const btn = document.getElementById('addLinkBtn');
        const text = document.getElementById('addBtnText');
        const loader = document.getElementById('addBtnLoader');
        
        if (loading) {
            btn.disabled = true;
            text.classList.add('hidden');
            loader.classList.remove('hidden');
        } else {
            btn.disabled = false;
            text.classList.remove('hidden');
            loader.classList.add('hidden');
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 glass-card p-4 max-w-sm transform transition-all duration-300 translate-x-full`;
        
        const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
        
        notification.innerHTML = `
            <div class="flex items-center space-x-3">
                <div class="w-2 h-2 rounded-full ${bgColor}"></div>
                <p class="text-sm font-medium text-gray-800 dark:text-white">${message}</p>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Animate out and remove
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Data management
    saveLinks() {
        localStorage.setItem('links', JSON.stringify(this.links));
    }

    exportLinks() {
        const dataStr = JSON.stringify(this.links, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `link-manager-export-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
        this.showNotification('Links exported successfully!', 'success');
        document.getElementById('dropdownMenu').classList.add('hidden');
    }

    importLinks() {
        document.getElementById('fileInput').click();
        document.getElementById('dropdownMenu').classList.add('hidden');
    }

    handleFileImport(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedLinks = JSON.parse(e.target.result);
                if (Array.isArray(importedLinks)) {
                    this.links = [...this.links, ...importedLinks];
                    this.saveLinks();
                    this.renderLinks();
                    this.updateFilters();
                    this.showNotification(`Imported ${importedLinks.length} links successfully!`, 'success');
                } else {
                    throw new Error('Invalid file format');
                }
            } catch (error) {
                this.showNotification('Failed to import links. Please check the file format.', 'error');
            }
        };
        reader.readAsText(file);
        
        // Reset file input
        e.target.value = '';
    }
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Global functions for onclick handlers
function openAddModal() {
    linkManager.openAddModal();
}

function closeAddModal() {
    linkManager.closeAddModal();
}

function closeEditModal() {
    linkManager.closeEditModal();
}

// Initialize the app
const linkManager = new LinkManager();
